import { Component, EventEmitter, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { skipWhile, take, takeUntil } from 'rxjs';

import { DAYS, EMPTY_GUID, OTP_USER_OPTIONS, VALIDATION_CLEAR, VALIDATION_SET } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import { assignToSort, changeCalendar, getSystemTimeZoneId, onPickerOpened, patchTime, setTimeZoneTime, toggleValidation, validateAllFormFields } from 'src/app/core/utils/common.util';
import { FetchAttendanceSetting, FetchNotification, UpdateAttendanceSetting } from 'src/app/reducers/attendance/attendance.actions';
import { getAttendanceSettings, getAttendanceSettingsIsloading } from 'src/app/reducers/attendance/attendance.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { AddShiftTiming, FetchBulkShiftTiming } from 'src/app/reducers/shift-timing/shift-timing.actions';
import { getBulkShiftTiming, getBulkShiftTimingIsLoading, getUpdateSTIsLoading } from 'src/app/reducers/shift-timing/shift-timing.reducers';
import { FetchWithoutAdmins } from 'src/app/reducers/teams/teams.actions';
import { getUserBasicDetails, getWithoutAdmins } from 'src/app/reducers/teams/teams.reducer';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';

@Component({
  selector: 'attendance-settings',
  templateUrl: './attendance-settings.component.html',
})
export class AttendanceSettingsComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  canUpdate: boolean;
  canView: boolean;
  userAccess: string;
  attendanceSettingsForm: FormGroup;
  userSettingsForm: FormGroup;
  showUserDropdown: boolean = false;
  isChangesMade: boolean = false;
  isChanges: boolean = false;
  withoutAdminList: any[];
  isUsersListForReassignmentLoading: boolean = true;
  days = DAYS;
  systemTimeZone: any;
  originalCheckboxValues: { [key: string]: boolean } = {};
  originalFromValues: { [key: string]: any } = {};
  originalToValues: { [key: string]: any } = {};
  userTypeOptions = OTP_USER_OPTIONS;
  selectedUsers: any[] = [];
  message: string;
  notes: string;
  selectedUserIds: any[] = [];
  shiftTimings: any[] = [];
  canApplyShitTimeForAll: boolean = false;
  shiftTimingIsLoading: boolean = false;
  shiftTimingUserIsLoading: boolean = false;
  userData: any;
  currentDate: Date = new Date();
  onPickerOpened = onPickerOpened

  constructor(
    private headerTitle: HeaderTitleService,
    private _store: Store<AppState>,
    private fb: FormBuilder,
    private modalService: BsModalService,
    private modalRef: BsModalRef,
    public metaTitle: Title
  ) { }

  ngOnInit(): void {
    this.attendanceSettingsForm = this.fb.group({
      ...this.createDayControls(DAYS),
      shiftTiming: [null],
      limitAccess: [null],
      selfieUploadClockin: [false],
      selfieUploadClockout: [false],
    });

    this.userSettingsForm = this.fb.group({
      userOption: [null],
      userList: [null],
    });
    this.metaTitle.setTitle('CRM | Global Config');
    this.headerTitle.setLangTitle('SIDEBAR.global-config');
    this.patchValue();

    this._store.dispatch(new FetchNotification());
    this._store.dispatch(new FetchAttendanceSetting());
    this._store.dispatch(new FetchBulkShiftTiming());
    this._store.dispatch(new FetchWithoutAdmins());

    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        const permissionsSet = new Set(permissions);
        this.canView = permissionsSet.has('Permissions.GlobalSettings.View');
        this.canUpdate = permissionsSet.has('Permissions.GlobalSettings.Update');
      });

    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(this.userData?.timeZoneInfo?.baseUTcOffset)
      });

    this._store
      .select(getBulkShiftTiming)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.shiftTimings = data;
        if (data) {
          const getShiftTime = (dayValue: number, timeType: 'shiftStartTime' | 'shiftEndTime') => {
            const shiftData = this.shiftTimings?.find((item: any) => item?.day === dayValue);
            return shiftData ? patchTime(shiftData[timeType], this.userData?.timeZoneInfo?.baseUTcOffset) : null;
          };
          const getIsEnabled = (dayValue: number) => {
            const shiftData = this.shiftTimings?.find((item: any) => item?.day === dayValue);
            return shiftData ? shiftData?.isEnabled : false;
          };

          const patchFormValue = (day: any) => {
            const shiftFrom = getShiftTime(day?.value, 'shiftStartTime');
            const shiftTo = getShiftTime(day?.value, 'shiftEndTime');
            const isEnabled = getIsEnabled(day?.value);
            const formValues: any = {
              [day?.id]: isEnabled,
              [day?.shiftFrom]: isEnabled ? shiftFrom : null,
              [day?.shiftTo]: isEnabled ? shiftTo : null,
            };
            this.attendanceSettingsForm?.patchValue(formValues);

          };


          DAYS?.forEach(day => patchFormValue(day));
        }
      });

    this._store
      .select(getBulkShiftTimingIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.shiftTimingIsLoading = isLoading;
      });

    this._store
      .select(getAttendanceSettingsIsloading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.shiftTimingUserIsLoading = isLoading;
      });

    this._store
      .select(getWithoutAdmins)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.withoutAdminList = data?.items?.items?.filter(
          (user: any) => user.isActive
        );
        this.withoutAdminList = this.withoutAdminList?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.withoutAdminList = assignToSort(this.withoutAdminList, '');
        this.patchValue();
      });

    this.userSettingsForm
      .get('userOption')
      .valueChanges.subscribe((value: boolean) => {
        if (value) {
          toggleValidation(
            VALIDATION_CLEAR,
            this.userSettingsForm,
            'userList'
          );
        } else {
          toggleValidation(
            VALIDATION_SET,
            this.userSettingsForm,
            'userList',
            [Validators.required]
          );
        }
      });

    DAYS.forEach(day => {
      this.attendanceSettingsForm?.get(day?.shiftFrom)?.valueChanges.subscribe((value) => {
        DAYS.forEach(day => {
          if ((value !== this.attendanceSettingsForm?.get(day?.shiftFrom).value) && value && this.canApplyShitTimeForAll) {
            this.attendanceSettingsForm.get(day?.shiftFrom).patchValue(value);
            this.attendanceSettingsForm.get(day?.shiftFrom)?.markAsTouched();
            this.attendanceSettingsForm.get(day?.shiftFrom)?.markAsDirty();
          }
        });
      });

      this.attendanceSettingsForm?.get(day?.shiftTo)?.valueChanges.subscribe((value) => {
        DAYS.forEach(day => {
          if ((value !== this.attendanceSettingsForm?.get(day?.shiftTo).value) && value && this.canApplyShitTimeForAll) {
            this.attendanceSettingsForm.get(day?.shiftTo).patchValue(value);
            this.attendanceSettingsForm.get(day?.shiftTo)?.markAsTouched();
            this.attendanceSettingsForm.get(day?.shiftTo)?.markAsDirty();
          }
        });
      });
    });

  }

  patchValue() {
    this._store
      .select(getAttendanceSettings)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.selectedUsers = data?.userIds?.map((id: any) => {
          return this.withoutAdminList?.find(user => user?.id === id);
        });
        this.attendanceSettingsForm?.patchValue({
          limitAccess: data?.isShiftTimeEnabled,
          shiftTiming: data?.isShiftTimingFeatureEnabled,
          selfieUploadClockin: data?.isSelfieMandatoryForClockIn,
          selfieUploadClockout: data?.isSelfieMandatoryForClockOut
        });

        this.userSettingsForm?.patchValue({
          userOption: data?.isEnabledForAllUsers,
          userList: data?.userIds,
        });

        if (data?.isEnabledForAllUsers) {
          this.showUserDropdown = false;
        }
        else {
          this.showUserDropdown = true;
        }
      });
  }

  createDayControls(days: any[]): { [key: string]: any } {
    return days.reduce((controls, day) => {
      controls[day.id] = [false];
      controls[day.shiftFrom] = [null];
      controls[day.shiftTo] = [null];
      return controls;
    }, {});
  }

  openConfirmModal(changePopup: any, settingType: string) {
    this.modalRef = this.modalService.show(changePopup, {
      class: 'modal-600 top-modal ip-modal-unset',
      ignoreBackdropClick: true,
      keyboard: false,
    });
    switch (settingType) {
      case 'shiftTiming':
        if (this.attendanceSettingsForm?.value?.shiftTiming) {
          this.message =
            'Are you sure you want to disable the “Shift Timing” option?';
          this.notes =
            'This will disable users from  shift Timing.';
        } else {
          this.message =
            'Are you sure you want to enable the “Shift Timing” option?';
          this.notes =
            'This will allow the users shift Timing';
        }
        break;
      case 'limitAccess':
        if (this.attendanceSettingsForm?.value?.limitAccess) {
          this.message =
            'Are you sure you want to disable the LimitAccess option?';
          this.notes =
            'This will disable users from limitAccess.';
        } else {
          this.message =
            'Are you sure you want to enable the LimitAccess option?';
          this.notes =
            'This will allow the users limitAccess';
        }
        break;
      case 'selfieUploadClockin':
        if (this.attendanceSettingsForm?.value?.selfieUploadClockin) {
          this.message =
            'Are you sure you want to disable the “SelfieUpload for Clockin” option?';
          this.notes =
            'This will disable users to upload selfie while clockin.';
        } else {
          this.message =
            'Are you sure you want to enable the “SelfieUpload for Clockin” option?';
          this.notes =
            'This will allow the users to upload selfie while clockin';
        }
        break;
      case 'selfieUploadClockout':
        if (this.attendanceSettingsForm?.value?.selfieUploadClockout) {
          this.message =
            'Are you sure you want to disable the “SelfieUpload for Clockout” option?';
          this.notes =
            'This will disable users to upload selfie while clockout.';
        } else {
          this.message =
            'Are you sure you want to enable the “SelfieUpload for Clockout” option?';
          this.notes =
            'This will allow the users to upload selfie while clockout';
        }
        break;
    }
  }

  onToggleChange(day: any) {
    const dayControl = this.attendanceSettingsForm?.get(day?.id);
    const shiftFromControl = this.attendanceSettingsForm?.get(day?.shiftFrom);
    const shiftToControl = this.attendanceSettingsForm?.get(day?.shiftTo);

    if (dayControl?.value) {
      shiftFromControl?.setValidators([Validators.required]);
      shiftToControl?.setValidators([Validators.required]);
    } else {
      shiftFromControl?.clearValidators();
      shiftToControl?.clearValidators();
      shiftFromControl?.setValue(null);
      shiftToControl?.setValue(null);
    }
    shiftFromControl?.updateValueAndValidity();
    shiftToControl?.updateValueAndValidity();
    this.isChangesMade = true;
  }

  toggleSelectUsersDropdown(type: any) {
    this.userAccess = type?.displayName;
    if (type?.displayName === 'Select Users') {
      this.showUserDropdown = true;
    }
    else {
      this.showUserDropdown = false;
    }
    this.isChanges = true;
  }

  updateSelectedUsers(event: any) {
    this.selectedUserIds = event?.map((user: any) => user.id);
    this.selectedUsers = [];
    for (const item of event) {
      this.selectedUsers?.push(item);
    }
    this.isChanges = true;
  }

  getWeekOffs(): number[] {
    return this.days?.reduce((weekOffs, day, index) => {
      if (!this.attendanceSettingsForm?.get(day?.id)?.value) {
        weekOffs?.push(index);
      }
      return weekOffs;
    }, []);
  }


  onSave() {
    if (!this.attendanceSettingsForm?.valid) {
      validateAllFormFields(this.attendanceSettingsForm);
      return;
    }

    const dtos: any[] = [];
    const shiftTimeIdMap = new Map<number, string>();

    this.shiftTimings?.forEach((item: any) => {
      if (item?.day !== undefined && item?.id !== undefined) {
        shiftTimeIdMap.set(item?.day, item?.id);
      }
    });

    const weekOffs = this.getWeekOffs();

    this.days.forEach(day => {
      const fromControlName = day?.shiftFrom;
      const toControlName = day?.shiftTo;
      const fromValue = this.attendanceSettingsForm?.get(fromControlName)?.value;
      const toValue = this.attendanceSettingsForm?.get(toControlName)?.value;

      const fromDate = new Date(fromValue);
      const toDate = new Date(toValue);

      const isEnabled = !weekOffs?.includes(day?.value);

      const payload = {
        shiftStartTime: setTimeZoneTime(fromDate, this.userData?.timeZoneInfo?.baseUTcOffset) || '',
        shiftEndTime: setTimeZoneTime(toDate, this.userData?.timeZoneInfo?.baseUTcOffset) || '',
        timeZoneId: this.userData?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
        weekOffs: weekOffs || [],
        userId: EMPTY_GUID,
        day: day?.value,
        isEnabled: isEnabled,
        id: shiftTimeIdMap.get(day.value) ? shiftTimeIdMap.get(day?.value) : EMPTY_GUID
      };
      dtos.push(payload);
    });

    if (dtos.length > 0) {
      const dtoObject = {
        dtos: dtos
      };
      this.shiftTimingIsLoading = true;
      this._store.dispatch(new AddShiftTiming(dtoObject));
      this._store
        .select(getUpdateSTIsLoading)
        .pipe(skipWhile((isLoading: boolean) => isLoading), take(1))
        .subscribe(() => {
          this.shiftTimingIsLoading = false;
          this.isChangesMade = false;
        });
    }
  }

  onSaveUser() {
    const settingsData: any = this.attendanceSettingsForm.value;
    const userSetting: any = this.userSettingsForm?.value;
    const payload = {
      isShiftTimingFeatureEnabled: settingsData?.shiftTiming,
      isShiftTimeEnabled: settingsData?.shiftTiming === true ? settingsData?.limitAccess : false,
      isNotificationEnabled: settingsData?.limitAccess === true ? true : false,
      isEnabledForAllUsers: false,
      isSelfieMandatoryForClockIn: settingsData?.selfieUploadClockin,
      IsSelfieMandatoryForClockOut: settingsData?.selfieUploadClockout,
      userIds: this.userAccess === 'Select all' ? [] : userSetting?.userList
    };
    if (settingsData?.shiftTiming) {
      payload.isEnabledForAllUsers = this.userAccess === 'Select all' ? true : false;
      payload.userIds = this.userAccess === 'Select all' ? [] : userSetting?.userList;
    } else {
      payload.isEnabledForAllUsers = false;
      payload.userIds = [];
    }
    this.shiftTimingUserIsLoading = true;
    this._store.dispatch(new UpdateAttendanceSetting(payload));
    this._store
      .select(getAttendanceSettingsIsloading)
      .pipe(skipWhile((isLoading: boolean) => isLoading), take(1))
      .subscribe(() => {
        this.shiftTimingUserIsLoading = false;
        this.isChanges = false;
        this.modalRef.hide();
      });
  }

  RemoveUserFromGroup(user: any) {
    this.isChanges = true;
    let filteredUsers = this.selectedUsers.filter((selectedUser) => {
      return selectedUser?.fullName !== user?.fullName;
    });

    this.selectedUsers = filteredUsers;
    const userIds = filteredUsers.map(user => user.id);
    this.userSettingsForm.get('userList').setValue(userIds);
  }

  applyToAllDays(event: any) {
    const isChecked = event?.target?.checked;
    this.canApplyShitTimeForAll = isChecked;
    if (isChecked) {
      DAYS.forEach(day => {
        if (!this.attendanceSettingsForm?.get(day?.id)?.value) {
          this.onToggleChange(day?.id);
          this.attendanceSettingsForm.get(day?.id).patchValue(true);
        }
        this.originalCheckboxValues[day?.id] = this.attendanceSettingsForm?.get(day?.id)?.value;
        this.originalFromValues[day?.id] = this.attendanceSettingsForm?.get(day?.shiftFrom)?.value;
        this.originalToValues[day?.id] = this.attendanceSettingsForm?.get(day?.shiftTo)?.value;
      });

      const firstToggledDay = DAYS?.find(day => this.attendanceSettingsForm?.get(day.id)?.value);
      if (firstToggledDay) {
        const fromValue = this.attendanceSettingsForm?.get(firstToggledDay?.shiftFrom)?.value;
        const toValue = this.attendanceSettingsForm?.get(firstToggledDay?.shiftTo)?.value;

        DAYS.forEach(day => {
          this.attendanceSettingsForm?.get(day?.shiftFrom)?.patchValue(fromValue);
          this.attendanceSettingsForm?.get(day?.shiftTo)?.patchValue(toValue);
        });
      }
    }
    this.isChangesMade = true;
  }

  onCancel() {
    if (this.isChangesMade) {
      this._store.dispatch(new FetchBulkShiftTiming());
      this.isChangesMade = false;
    }
    else {
      this._store.dispatch(new FetchAttendanceSetting());
      this.isChanges = false;
    }
  }

  closePopup() {
    this.patchValue();
    this.modalRef.hide();
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
