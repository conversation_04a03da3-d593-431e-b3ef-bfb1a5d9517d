<ng-container *ngIf="canViewDashboard">
    <div class="w-100 d-flex">
        <!-- Dashboard without <PERSON>mind<PERSON> -->
        <div>
            <!-- Top-nav -->
            <div class="bg-dark w-100 fw-600 text-gray-850">
                <div [ngClass]="showLeftNav ? 'left-150' : 'left-40'"
                    class="dashboard mx-20 ip-d-none d-flex position-absolute top-10">
                    <h4 class="text-white fw-semi-bold text-truncate max-w-200 mt-6">{{orgName}}</h4>
                    <ng-select [virtualScroll]="true" [items]="dashboardTypes" [searchable]="false" [clearable]="false"
                        ResizableDropdown name="dashboardList" class="ph-w-100px ml-10"
                        [(ngModel)]="dashboardVisibility" (change)="onDashboardSelection()">
                    </ng-select>
                    <div *ngIf="dashboardVisibility!==dashboardTypes[0]" class="align-center">
                        <div class="dashboard-input">
                            <ng-select [virtualScroll]="true" placeholder="{{'SIDEBAR.my-team'|translate}}"
                                [items]="usersList" ResizableDropdown [multiple]="true" [closeOnSelect]="false"
                                bindLabel="fullName" bindValue="id" name="dashboardUsers"
                                (close)="onDashboardSelection()" (remove)="onDashboardSelection()"
                                (clear)="onDashboardSelection()" [(ngModel)]="userIds"
                                class="w-160 ml-10 text-white ip-w-100px">
                                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span>{{item.firstName}} {{item.lastName}} <span
                                            class="error-message-custom top-13" *ngIf="!item.isActive">( Disabled
                                            )</span>
                                    </div>
                                </ng-template>
                            </ng-select>
                        </div>
                        <label class="checkbox-container mb-4 ml-10">
                            <input type="checkbox" [(ngModel)]="withTeam" (change)="onDashboardSelection()">
                            <span class="checkmark"></span><span class="text-white">{{'DASHBOARD.with-team' |
                                translate}}</span>
                        </label>
                    </div>
                </div>
                <div class="dashboard d-none ip-d-block">
                    <div (click)="showFilters = !showFilters"
                        class="icon ic-filter-solid mt-16 left-30 cursor-pointer tb-position-absolute top-0"></div>
                    <div *ngIf="showFilters"
                        class="z-index-1001 align-end tb-flex-end left-30 tb-flex-wrap tb-position-absolute tb-left-8 bg-dark tb-w-200 tb-br tb-px-10 tb-py-10">
                        <div class="flex-between tb-w-100">
                            <h5 class="fw-semi-bold text-light d-none tb-d-block">Filters</h5>
                            <div class="icon ic-close-secondary ic-sm cursor-pointer d-none tb-d-block"
                                (click)="showFilters = !showFilters">
                            </div>
                        </div>
                        <ng-select [virtualScroll]="true" [items]="dashboardTypes" [searchable]="false"
                            [clearable]="false" bindLabel="displayName" bindValue="value" name="dashboardList"
                            ResizableDropdown [(ngModel)]="dashboardVisibility" (change)="onDashboardSelection()"
                            class="h-30 tb-w-100 tb-mt-10 tb-mr-0">
                        </ng-select>
                        <div class="align-end tb-flex-wrap mt-8">
                            <ng-container *ngIf="dashboardVisibility !== dashboardTypes[0]">
                                <div class="dashboard-input ip-w-100">
                                    <ng-select [virtualScroll]="true" placeholder="{{'SIDEBAR.my-team'|translate}}"
                                        [items]="usersList" ResizableDropdown [multiple]="true" [closeOnSelect]="false"
                                        bindLabel="fullName" bindValue="id" name="dashboardUsers"
                                        (close)="onDashboardSelection()" (remove)="onDashboardSelection()"
                                        (clear)="onDashboardSelection()" [(ngModel)]="userIds"
                                        class=" text-white ph-w-100">
                                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                            <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                                    class="checkmark"></span>{{item.firstName}} {{item.lastName}} <span
                                                    class="error-message-custom top-13" *ngIf="!item.isActive">(
                                                    Disabled
                                                    )</span>
                                            </div>
                                        </ng-template>
                                    </ng-select>
                                </div>
                                <label class="checkbox-container mt-8 mb-4">
                                    <input type="checkbox" [(ngModel)]="withTeam" (change)="onDashboardSelection()"
                                        [disabled]="!userIds?.length" [title]="userIds?.length ? '' : 'Select user'">
                                    <span class="checkmark"></span>
                                    <span class="text-white">{{ 'DASHBOARD.with-team' | translate }}</span>
                                </label>
                            </ng-container>
                        </div>
                    </div>
                </div>
                <div class="flex-between">
                    <!-- All count -->
                    <drag-scroll class="scrollbar scroll-hide tb-w-100-113"
                        [ngClass]="showLeftNav ? 'w-100-490' : 'w-100-390'">
                        <div class="align-center text-nowrap ml-30 py-10">
                            <div>
                                <div class="text-sm">Total Leads</div>
                                <h2 class="text-white mt-2" *ngIf="!isCountByStatusLoading else loaderWhite">
                                    {{totalCount || 0}}</h2>
                            </div>
                            <div class="mx-20 border-dark-600 h-40"></div>
                            <div [ngClass]="leadsCountByStatus?.activeLeads && canViewLead ? 'cursor-pointer' : 'pe-none'"
                                (click)="onClickNavigateFromCount(leadVisibility[(dashboardType+1)%3].name, leadStatus?.active)">
                                <div class="fw-semi-bold text-sm">Active</div>
                                <h3 class="text-white" *ngIf="!isCountByStatusLoading else loaderWhite">
                                    {{leadsCountByStatus?.activeLeads || 0}}</h3>
                            </div>
                            <ng-container *ngIf="canViewUnassignedCount">
                                <div class="w-10 h-10 text-mud mx-20">+</div>
                                <div [ngClass]="leadsCountByStatus?.unassignedLeads && canViewLead ? 'cursor-pointer' : 'pe-none'"
                                    (click)="onClickNavigateFromCount(leadVisibility[3].name, leadStatus?.all)">
                                    <div class="fw-semi-bold text-sm">Unassigned</div>
                                    <h3 class="text-white" *ngIf="!isCountByStatusLoading else loaderWhite">
                                        {{leadsCountByStatus?.unassignedLeads || 0}}</h3>
                                </div>
                            </ng-container>
                            <ng-container *ngIf="canViewDeletedLeadsCount">
                                <div class="w-10 h-10 text-mud mx-20">+</div>
                                <div [ngClass]="leadsCountByStatus?.deletedLeads && canViewLead ? 'cursor-pointer' : 'pe-none'"
                                    (click)="onClickNavigateFromCount(leadVisibility[4].name, leadStatus?.all)">
                                    <div class="fw-semi-bold text-sm">Deleted</div>
                                    <h3 class="text-white" *ngIf="!isCountByStatusLoading else loaderWhite">
                                        {{leadsCountByStatus?.deletedLeads || 0}}</h3>
                                </div>
                            </ng-container>
                            <div class="w-10 h-10 text-mud mx-20">+</div>
                            <div [ngClass]="leadsCountByStatus?.booked && canViewLead ? 'cursor-pointer' : 'pe-none'"
                                (click)="onClickNavigateFromCount(leadVisibility[(dashboardType+1)%3].name, leadStatus?.booked)">
                                <div class="fw-semi-bold text-sm">Booked</div>
                                <h3 class="text-white" *ngIf="!isCountByStatusLoading else loaderWhite">
                                    {{leadsCountByStatus?.booked || 0}}</h3>
                            </div>
                            <div class="w-10 h-10 text-mud mx-20">+</div>
                            <div [ngClass]="leadsCountByStatus?.bookingCancel && canViewLead ? 'cursor-pointer' : 'pe-none'"
                                (click)="onClickNavigateFromCount(leadVisibility[(dashboardType+1)%3].name, leadStatus?.bookingCancel)">
                                <div class="fw-semi-bold text-sm">Booking cancel</div>
                                <h3 class="text-white" *ngIf="!isCountByStatusLoading else loaderWhite">
                                    {{leadsCountByStatus?.bookingCancel || 0}}</h3>
                            </div>
                            <div class="w-10 h-10 text-mud mx-20">+</div>
                            <div [ngClass]="leadsCountByStatus?.notInterested && canViewLead ? 'cursor-pointer' : 'pe-none'"
                                (click)="onClickNavigateFromCount(leadVisibility[(dashboardType+1)%3].name, leadStatus?.['not-interested'])">
                                <div class="fw-semi-bold text-sm">Not Interested</div>
                                <h3 class="text-white" *ngIf="!isCountByStatusLoading else loaderWhite">
                                    {{leadsCountByStatus?.notInterested || 0}}</h3>
                            </div>
                            <div class="w-10 h-10 text-mud mx-20">+</div>
                            <div [ngClass]="leadsCountByStatus?.dropped && canViewLead ? 'cursor-pointer' : 'pe-none'"
                                (click)="onClickNavigateFromCount(leadVisibility[(dashboardType+1)%3].name, leadStatus?.dropped)">
                                <div class="fw-semi-bold text-sm">Dropped</div>
                                <h3 class="text-white" *ngIf="!isCountByStatusLoading else loaderWhite">
                                    {{leadsCountByStatus?.dropped || 0}}</h3>
                            </div>
                        </div>
                    </drag-scroll>
                    <button
                        class="d-none tb-d-block p-4 text-white border-gray br-4 cursor-pointer mx-16 align-center btn btn-sm btn-linear-green"
                        (click)="showEvents = !showEvents"><span class="py-8">
                            {{'DASHBOARD.reminders' | translate}}</span>
                    </button>
                    <img src="../../../../assets/images/muso-hi.svg" alt="img"
                        class="mr-20 mt-10 position-relative d-block tb-d-none" width="50px" height="51px">
                </div>
            </div>
            <div class="d-flex">
                <div class="scrollbar scroll-hide h-100-108 position-relative" #scrollContainer>
                    <!-- <div title="Scroll To Bottom"
                        class="dot dot-lg bg-accent-green position-absolute right-5 top-5 cursor-pointer shadow"
                        (click)="scrollToBottom()">
                        <span class="icon ic-chevron-down ic-xxs mt-2"></span>
                    </div> -->
                    <!-- Active leads -->
                    <div class="flex-between ml-30 my-16 ph-ml-10">
                        <drag-scroll class="scrollbar scroll-hide tb-w-100-70 ph-w-100-45"
                            [ngClass]="showLeftNav ? 'w-100-455' : 'w-100-355'">
                            <div class="d-flex">
                                <div class="br-4 p-10 bg-white border border-start-0 mr-16 position-relative ">
                                    <div
                                        class="position-absolute border-right-purple-300 h-30px left-0 brbr-10 brtr-10">
                                    </div>
                                    <div class="flex-between"
                                        [ngClass]="leadsCountByStatus?.new && canViewLead ? 'cursor-pointer' : 'pe-none'"
                                        (click)="onClickNavigateFromCount(leadVisibility[(dashboardType+1)%3].name, leadStatus?.active, leadStatus?.new)">
                                        <div class="mr-10">
                                            <div class="fw-semi-bold text-xs text-purple-300">New</div>
                                            <h3 class="fw-700 mt-2" *ngIf="!isCountByStatusLoading else loader">
                                                {{leadsCountByStatus?.new || 0}}</h3>
                                        </div>
                                        <img src="../../../../assets/dashboard/plus.svg" alt="img" />
                                    </div>
                                </div>
                                <div class="br-4 p-10 bg-white border border-start-0 mr-16 position-relative ">
                                    <div class="position-absolute border-right-red-550 h-30px left-0 brbr-10 brtr-10">
                                    </div>
                                    <div class="flex-between"
                                        [ngClass]="leadsCountByStatus?.pending && canViewLead ? 'cursor-pointer' : 'pe-none'"
                                        (click)="onClickNavigateFromCount(leadVisibility[(dashboardType+1)%3].name, leadStatus?.active, leadStatus?.pending)">
                                        <div class="mr-10">
                                            <div class="fw-semi-bold text-xs text-red-550">Pending</div>
                                            <h3 class="fw-700 mt-2" *ngIf="!isCountByStatusLoading else loader">
                                                {{leadsCountByStatus?.pending || 0}}</h3>
                                        </div>
                                        <img src="../../../../assets/dashboard/clock.svg" alt="img" />
                                    </div>
                                </div>
                                <div class="br-4 p-10 bg-white border border-start-0 mr-16 position-relative ">
                                    <div class="position-absolute border-right-blue-700 h-30px left-0 brbr-10 brtr-10">
                                    </div>
                                    <div class="flex-between"
                                        [ngClass]="leadsCountByStatus?.callback && canViewLead ? 'cursor-pointer' : 'pe-none'"
                                        (click)="onClickNavigateFromCount(leadVisibility[(dashboardType+1)%3].name, leadStatus?.active, leadStatus?.scheduled, null, null, 'All', 'Callbacks')">
                                        <div class="mr-10">
                                            <div class="fw-semi-bold text-xs text-blue-700">Callbacks</div>
                                            <h3 class="fw-700 mt-2" *ngIf="!isCountByStatusLoading else loader">
                                                {{leadsCountByStatus?.callback || 0}}</h3>
                                        </div>
                                        <img src="../../../../assets/dashboard/call.svg" alt="img" />
                                    </div>
                                </div>
                                <div class="br-4 p-10 bg-white border border-start-0 mr-16 position-relative ">
                                    <div
                                        class="position-absolute border-right-green-550  h-30px left-0 brbr-10 brtr-10">
                                    </div>
                                    <div class="flex-between"
                                        [ngClass]="leadsCountByStatus?.meetingScheduled && canViewLead ? 'cursor-pointer' : 'pe-none'"
                                        (click)="onClickNavigateFromCount(leadVisibility[(dashboardType+1)%3].name, leadStatus?.active, leadStatus?.scheduled, null, null, 'All', 'Meetings')">
                                        <div class="mr-10">
                                            <div class="fw-semi-bold text-xs text-green-550">Meetings Scheduled</div>
                                            <h3 class="fw-700 mt-2" *ngIf="!isCountByStatusLoading else loader">
                                                {{leadsCountByStatus?.meetingScheduled || 0}}</h3>
                                        </div>
                                        <img src="../../../../assets/dashboard/calendar.svg" alt="img" />
                                    </div>
                                </div>
                                <div class="br-4 p-10 bg-white border border-start-0 mr-16 position-relative ">
                                    <div
                                        class="position-absolute border-right-yellow-500 h-30px left-0 brbr-10 brtr-10">
                                    </div>
                                    <div class="flex-between"
                                        [ngClass]="leadsCountByStatus?.siteVisitScheduled && canViewLead ? 'cursor-pointer' : 'pe-none'"
                                        (click)="onClickNavigateFromCount(leadVisibility[(dashboardType+1)%3].name, leadStatus?.active, leadStatus?.scheduled, null, null, 'All', 'Site visits')">
                                        <div class="mr-10">
                                            <div class="fw-semi-bold text-xs text-yellow-500">Site Visits Scheduled
                                            </div>
                                            <h3 class="fw-700 mt-2" *ngIf="!isCountByStatusLoading else loader">
                                                {{leadsCountByStatus?.siteVisitScheduled || 0}}</h3>
                                        </div>
                                        <img src="../../../../assets/dashboard/location.svg" alt="img" />
                                    </div>
                                </div>
                                <div class="br-4 p-10 bg-white border border-start-0 mr-16 position-relative ">
                                    <div class="position-absolute border-right-red-110 h-30px left-0 brbr-10 brtr-10">
                                    </div>
                                    <div class="flex-between"
                                        [ngClass]="leadsCountByStatus?.overdue && canViewLead ? 'cursor-pointer' : 'pe-none'"
                                        (click)="onClickNavigateFromCount(leadVisibility[(dashboardType+1)%3].name, leadStatus?.active, leadStatus?.overdue)">
                                        <div class="mr-10">
                                            <div class="fw-semi-bold text-xs text-red-110">Overdue</div>
                                            <h3 class="fw-700 mt-2" *ngIf="!isCountByStatusLoading else loader">
                                                {{leadsCountByStatus?.overdue || 0}}</h3>
                                        </div>
                                        <img src="../../../../assets/dashboard/alarm.svg" alt="img" />
                                    </div>
                                </div>
                                <div class="br-4 p-10 bg-white border border-start-0 mr-16 position-relative ">
                                    <div class="position-absolute h-30px left-0 brbr-10 brtr-10"
                                        style="border-right: 2px solid #0A9396;"></div>
                                    <div class="flex-between"
                                        [ngClass]="leadsCountByStatus?.expressionOfInterest && canViewLead ? 'cursor-pointer' : 'pe-none'"
                                        (click)="onClickNavigateFromCount(leadVisibility[(dashboardType+1)%3].name, leadStatus?.active, leadStatus?.expressionOfInterestLeadCount)">
                                        <div class="mr-10">
                                            <div class="fw-semi-bold text-xs" style="color: #0A9396;">Expression Of
                                                Interest
                                            </div>
                                            <h3 class="fw-700 mt-2" *ngIf="!isCountByStatusLoading else loader">
                                                {{leadsCountByStatus?.expressionOfInterest || 0}}</h3>
                                        </div>
                                        <img src="../../../../assets/dashboard/EOI.svg" alt="img" />
                                    </div>
                                </div>
                                <div class="br-4 px-2 h-50px mr-16 my-2 bg-slate-250"></div>
                                <div class="br-4 p-10 bg-white border border-start-0 mr-16 position-relative ">
                                    <div class="position-absolute h-30px left-0 brbr-10 brtr-10"
                                        style="border-right: 2px solid #286D34;"></div>
                                    <div class="flex-between"
                                        [ngClass]="leadsCountByStatus?.meetingDone && canViewLead ? 'cursor-pointer' : 'pe-none'"
                                        (click)="onClickNavigateFromCount(leadVisibility[(dashboardType+1)%3].name, leadStatus?.all, null, null, null, null, null, 'Meeting Done')">
                                        <div class="mr-10">
                                            <div class="fw-semi-bold text-xs" style="color: #286D34;">Meeting done</div>
                                            <h3 class="fw-700 mt-2" *ngIf="!isCountByStatusLoading else loader">
                                                {{leadsCountByStatus?.meetingDone ||
                                                0}}{{leadsCountByStatus?.meetingDoneUniqueCount ?
                                                ' (' + leadsCountByStatus?.meetingDoneUniqueCount + ')' : ''}}</h3>
                                        </div>
                                        <img src="../../../../assets/dashboard/users-meet.svg" alt="img" />
                                    </div>
                                </div>
                                <div class="br-4 p-10 bg-white border border-start-0 position-relative ">
                                    <div class="position-absolute h-30px left-0 brbr-10 brtr-10"
                                        style="border-right: 2px solid #AC7D21;"></div>
                                    <div class="flex-between"
                                        [ngClass]="leadsCountByStatus?.siteVisitDone && canViewLead ? 'cursor-pointer' : 'pe-none'"
                                        (click)="onClickNavigateFromCount(leadVisibility[(dashboardType+1)%3].name, leadStatus?.all, null, null, null, null, null, 'Site Visit Done')">
                                        <div class="mr-10">
                                            <div class="fw-semi-bold text-xs" style="color: #AC7D21;">Site visit done
                                            </div>
                                            <h3 class="fw-700 mt-2" *ngIf="!isCountByStatusLoading else loader">
                                                {{leadsCountByStatus?.siteVisitDone ||
                                                0}}{{leadsCountByStatus?.siteVisitDoneUniqueCount ? ' (' +
                                                leadsCountByStatus?.siteVisitDoneUniqueCount + ')' : ''}}</h3>
                                        </div>
                                        <img src="../../../../assets/dashboard/location-user.svg" alt="img" />
                                    </div>
                                </div>
                            </div>
                        </drag-scroll>
                    </div>
                    <div class="pl-30 ph-pl-10 tb-w-100-34" [ngClass]="showLeftNav ? 'w-100-423' : 'w-100-323'">
                        <!-- level 1 - Source -->
                        <ng-container *ngIf="canViewLeadSource">
                            <div class="bg-white w-100">
                                <div class="py-6 px-10 flex-between border-bottom">
                                    <div class="flex-center text-nowrap">
                                        <div class="text-black-200">Leads From Source</div>
                                        <ng-container *ngIf="showCategoryUI else detailCount">
                                            <ng-container *ngIf="!isCountByStatusLoading else loader">
                                                <div class="fw-semi-bold text-black-200 mx-4">
                                                    ({{allLeadsCount || 0}})
                                                </div>
                                            </ng-container>
                                        </ng-container>
                                        <ng-template #detailCount>
                                            <ng-container *ngIf="!isSourceDetailsLoading else loader">
                                                <div class="fw-semi-bold text-black-200 mx-4">
                                                    ({{stackData?.[0]?.total}})
                                                </div>
                                            </ng-container>
                                        </ng-template>
                                        <div class="dot dot-sm bg-black-200 cursor-pointer"
                                            title="This Graph will give you the segregated count of Leads in the System Based on source you can also filter it out based on date.">
                                            <span class="m-auto text-white">?</span>
                                        </div>
                                    </div>
                                    <div class="align-center">
                                        <label class="checkbox-container text-normal ph-mb-16">
                                            <input type="checkbox" [(ngModel)]="isAssociatedData"
                                                (change)="updateDataFlags()" />
                                            <span class="checkmark"></span><span class="ph-d-none">Associated
                                                Data</span></label>
                                        <div class="border-dark-600 h-10 mx-10"></div>
                                        <!-- <div class="fw-semi-bold mr-8 ph-d-none">{{showCategoryUI ? 'Category':
                                            'Detail'}}
                                            View</div>
                                        <div class="cursor-pointer" (click)=" showCategoryUI = !showCategoryUI">
                                            <img [src]="showCategoryUI ? '../../../../assets/images/category-view.svg' : '../../../../assets/images/detail-view.svg' "
                                                alt="Image">
                                        </div>
                                        <div class="border-dark-600 h-10 mx-10"></div> -->
                                        <ng-container *ngIf="showCategoryUI else detailFilter">
                                            <div class="position-relative">
                                                <div class="flex-center bg-violet-600 p-6 cursor-pointer"
                                                    (click)="onFilterClick('showSourceFilter')">
                                                    <span
                                                        class="icon ic-calendar-minus ic-xxs ic-black-200 cursor-pointer mr-4 ip-mr-10"></span>
                                                    <span
                                                        class="text-black-200 mr-12 text-xs text-nowrap ip-d-none">{{sourceForm.controls['sourceRange'].value}}</span>
                                                    <span class="ic-triangle-down icon ic-black-200 ic-xx-xs"></span>
                                                </div>
                                                <form [formGroup]="sourceForm"
                                                    class="position-absolute top-30 w-270 bg-white right-0 z-index-1001 box-shadow-10"
                                                    *ngIf="filters.showSourceFilter">
                                                    <div class="d-flex">
                                                        <div class="w-35 bg-light-slate">
                                                            <div
                                                                class="box-shadow-4 py-6 px-10 fw-semi-bold text-sm text-black-200">
                                                                Date Type
                                                            </div>
                                                            <div>
                                                                <ng-container *ngFor="let type of dateTypeFilterList">
                                                                    <div class="form-check form-check-inline p-6">
                                                                        <input type="radio" id="inpSource{{type.value}}"
                                                                            name="sourceDType"
                                                                            formControlName="sourceDType"
                                                                            [value]="type.value"
                                                                            class="radio-check-input w-8 h-8 mr-8">
                                                                        <label
                                                                            class="text-dark-gray cursor-pointer text-large text-sm"
                                                                            for="inpSource{{type.value}}">{{type.displayName}}</label>
                                                                    </div>
                                                                </ng-container>
                                                            </div>
                                                        </div>
                                                        <div class="w-65 bg-light-pearl">
                                                            <div
                                                                class="box-shadow-4 py-6 px-10 fw-semi-bold text-sm text-black-200">
                                                                Range
                                                            </div>
                                                            <ng-container *ngFor="let type of dateFilterList">
                                                                <div class="form-check form-check-inline p-6">
                                                                    <input type="radio" id="inpSource{{type.value}}"
                                                                        name="sourceRange" formControlName="sourceRange"
                                                                        [value]="type.value"
                                                                        class="radio-check-input w-8 h-8 mr-8">
                                                                    <label
                                                                        class="text-dark-gray cursor-pointer text-large text-sm"
                                                                        for="inpSource{{type.value}}">{{type.displayName}}</label>
                                                                </div>
                                                            </ng-container>
                                                            <div class="position-relative dashboard-filter form-group m-6 mb-16"
                                                                [ngClass]="{'pe-none disabled' : sourceForm.controls['sourceRange'].value !== 'Custom'}">
                                                                <form-errors-wrapper
                                                                    [control]="sourceForm.controls['sourceDate']"
                                                                    label="Date">
                                                                    <input type="text" readonly
                                                                        [owlDateTimeTrigger]="dt1" [owlDateTime]="dt1"
                                                                        [selectMode]="'range'"
                                                                        formControlName="sourceDate"
                                                                        placeholder="Select date" />
                                                                    <owl-date-time [pickerType]="'calendar'"
                                                                        (afterPickerOpen)="onPickerOpened(currentDate)"
                                                                        #dt1></owl-date-time>
                                                                </form-errors-wrapper>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="flex-end p-6">
                                                        <h6 class="fw-semi-bold text-black-200 text-decoration-underline mr-10 cursor-pointer"
                                                            (click)="filters.showSourceFilter = false">
                                                            Cancel</h6>
                                                        <div class="btn-coal" (click)="onSourceChange()">Apply</div>
                                                    </div>
                                                </form>
                                            </div>
                                        </ng-container>
                                        <ng-template #detailFilter>
                                            <div class="position-relative">
                                                <div class="flex-center bg-violet-600 p-6 cursor-pointer"
                                                    (click)="onFilterClick('showSource2Filter')">
                                                    <span
                                                        class="icon ic-calendar-minus  ic-xxs ic-black-200 cursor-pointer mr-4 ip-mr-10"></span>
                                                    <span
                                                        class="text-black-200 mr-12 text-xs text-nowrap ip-d-none">{{source2Form.controls['source2Range'].value}}</span>
                                                    <span class="ic-triangle-down icon ic-black-200 ic-xx-xs"></span>
                                                </div>
                                                <form [formGroup]="source2Form"
                                                    class="position-absolute top-30 w-270 bg-white right-0 z-index-1001 box-shadow-10"
                                                    *ngIf="filters.showSource2Filter">
                                                    <div class="d-flex">
                                                        <div class="w-35 bg-light-slate">
                                                            <div
                                                                class="box-shadow-4 py-6 px-10 fw-semi-bold text-sm text-black-200">
                                                                Date Type
                                                            </div>
                                                            <ng-container *ngFor="let type of dateTypeFilterList">
                                                                <div class="form-check form-check-inline p-6">
                                                                    <input type="radio" id="inpSource2{{type.value}}"
                                                                        name="source2DType"
                                                                        formControlName="source2DType"
                                                                        [value]="type.value"
                                                                        class="radio-check-input w-8 h-8 mr-8">
                                                                    <label
                                                                        class="text-dark-gray cursor-pointer text-large text-sm"
                                                                        for="inpSource2{{type.value}}">{{type.displayName}}</label>
                                                                </div>
                                                            </ng-container>
                                                        </div>
                                                        <div class="w-65 bg-light-pearl">
                                                            <div
                                                                class="box-shadow-4 py-6 px-10 fw-semi-bold text-sm text-black-200">
                                                                Range
                                                            </div>
                                                            <div>
                                                                <ng-container *ngFor="let type of dateFilterList">
                                                                    <div class="form-check form-check-inline p-6">
                                                                        <input type="radio"
                                                                            id="inpSource2{{type.value}}"
                                                                            name="source2Range"
                                                                            formControlName="source2Range"
                                                                            [value]="type.value"
                                                                            class="radio-check-input w-8 h-8 mr-8">
                                                                        <label
                                                                            class="text-dark-gray cursor-pointer text-large text-sm"
                                                                            for="inpSource2{{type.value}}">{{type.displayName}}</label>
                                                                    </div>
                                                                </ng-container>
                                                            </div>
                                                            <div class="position-relative dashboard-filter form-group m-6 mb-16"
                                                                [ngClass]="{'pe-none disabled' : source2Form.controls['source2Range'].value !== 'Custom'}">
                                                                <form-errors-wrapper
                                                                    [control]="source2Form.controls['source2Date']"
                                                                    label="Date">
                                                                    <input type="text" readonly
                                                                        [owlDateTimeTrigger]="dt1" [owlDateTime]="dt1"
                                                                        [selectMode]="'range'"
                                                                        formControlName="source2Date"
                                                                        placeholder="Select date" />
                                                                    <owl-date-time [pickerType]="'calendar'"
                                                                        (afterPickerOpen)="onPickerOpened(currentDate)"
                                                                        #dt1></owl-date-time>
                                                                </form-errors-wrapper>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="flex-end p-6">
                                                        <h6 class="fw-semi-bold text-black-200 text-decoration-underline mr-10 cursor-pointer"
                                                            (click)="filters.showSource2Filter = false">
                                                            Cancel</h6>
                                                        <div class="btn-coal" (click)="onSource2Change()">Apply</div>
                                                    </div>
                                                </form>
                                            </div>
                                        </ng-template>
                                    </div>
                                </div>
                                <!-- source stack chart -->
                                <ng-container *ngIf="!showCategoryUI">
                                    <ng-container *ngIf="!isSourceDetailsLoading else skeletonLoader">
                                        <ng-container *ngIf="!isAllDataPointsEmpty else noData">
                                            <div class="py-20">
                                                <canvasjs-chart
                                                    [options]="chartOptions?.sourceChartOptions"></canvasjs-chart>
                                            </div>
                                        </ng-container>
                                    </ng-container>
                                </ng-container>
                                <!-- source progress bars -->
                                <div class="w-100 py-6 px-10 d-flex flex-wrap scrollbar max-h-310 min-h-300"
                                    *ngIf="showCategoryUI">
                                    <ng-container *ngIf="hasAssociatedData else noSourceData">
                                        <!-- Social Profiles -->
                                        <div class="w-33 tb-w-50 ph-w-100 fw-semi-bold">
                                            <div class="text-sm text-light-slate text-decoration-underline mb-6">
                                                social profiles</div>
                                            <ng-container *ngIf="!isSourceLoading else shimmerLoader">
                                                <ng-container *ngIf="hasSPData else noAssociatedData">
                                                    <ng-container *ngFor="let profile of socialProfileSource">
                                                        <div *ngIf="(!isAssociatedData || (isAssociatedData && profile.count > 0))"
                                                            class="h-35px mb-10 mr-10 ph-mr-0 position-relative shadow-hover-sm"
                                                            [ngClass]="profile.count && canViewLead ? 'cursor-pointer' : 'pe-none'"
                                                            [ngStyle]="{'background-color': profile.backgroundColor || '#EBEEF1'}"
                                                            (click)="onClickNavigateFromCount(leadVisibility[(dashboardType+1)%3].name, leadStatus?.all, null, null, profile.leadSource, null, null, null, false, [null, null], 'source')">
                                                            <div class="position-absolute h-100 top-0 left-0"
                                                                [style.width]="(profile.count / allLeadsCount) * 100 + '%'"
                                                                [ngStyle]="{'background-color': profile.progressColor || '#CDD5DB'}">
                                                            </div>
                                                            <div
                                                                class="flex-between position-absolute w-100 h-100 top-0 left-0 p-10">
                                                                <div class="align-center">
                                                                    <img [type]="'leadrat'"
                                                                        [appImage]="profile.imageURL ? s3BucketUrl+profile.imageURL : ''"
                                                                        alt="logo" width=14px height=14px class="mr-10">
                                                                    <div class="text-sm">
                                                                        {{LeadSource[profile.leadSource]}}
                                                                    </div>
                                                                </div>
                                                                <h5>{{ profile.count }}</h5>
                                                            </div>
                                                        </div>
                                                    </ng-container>
                                                </ng-container>
                                            </ng-container>
                                        </div>

                                        <!-- Third Parties -->
                                        <div class="w-33 tb-w-50 ph-w-100 fw-semi-bold">
                                            <div class="text-sm text-light-slate text-decoration-underline mb-6">3rd
                                                parties
                                            </div>
                                            <ng-container *ngIf="!isSourceLoading else shimmerLoader">
                                                <ng-container *ngIf="hasTPData else noAssociatedData">
                                                    <ng-container *ngFor="let party of thirdPartySource">
                                                        <div *ngIf="(!isAssociatedData || (isAssociatedData && party.count > 0))"
                                                            class="h-35px mb-10 mr-10 ip-mr-0 position-relative shadow-hover-sm"
                                                            [ngClass]="party.count && canViewLead ? 'cursor-pointer' : 'pe-none'"
                                                            [ngStyle]="{'background-color': party.backgroundColor || '#EBEEF1'}"
                                                            (click)="onClickNavigateFromCount(leadVisibility[(dashboardType+1)%3].name, leadStatus?.all, null, null, party.leadSource, null, null, null, false, [null, null], 'source')">
                                                            <div class="position-absolute h-100 top-0 left-0"
                                                                [style.width]="(party.count / allLeadsCount) * 100 + '%'"
                                                                [ngStyle]="{'background-color': party.progressColor || '#CDD5DB'}">
                                                            </div>
                                                            <div
                                                                class="flex-between position-absolute w-100 h-100 top-0 left-0 p-10">
                                                                <div class="align-center">
                                                                    <img [type]="'leadrat'"
                                                                        [appImage]="party.imageURL ? s3BucketUrl+party.imageURL : ''"
                                                                        alt="logo" width=14px height=14px class="mr-10">
                                                                    <div class="text-sm">{{ LeadSource[party.leadSource]
                                                                        }}
                                                                    </div>
                                                                </div>
                                                                <h5>{{ party.count }}</h5>
                                                            </div>
                                                        </div>
                                                    </ng-container>
                                                </ng-container>
                                            </ng-container>
                                        </div>

                                        <!-- Others -->
                                        <div class="w-33 tb-w-50 ph-w-100 fw-semi-bold">
                                            <div class="text-sm text-light-slate text-decoration-underline mb-6">others
                                            </div>
                                            <ng-container *ngIf="!isSourceLoading else shimmerLoader">
                                                <ng-container *ngIf="hasOthersData else noAssociatedData">
                                                    <ng-container *ngFor="let others of othersSource">
                                                        <div *ngIf="(!isAssociatedData || (isAssociatedData && others.count > 0))"
                                                            class="h-35px mb-10 position-relative shadow-hover-sm"
                                                            [ngClass]="others.count && canViewLead ? 'cursor-pointer' : 'pe-none'"
                                                            [ngStyle]="{'background-color': others.backgroundColor || '#EBEEF1'}"
                                                            (click)="onClickNavigateFromCount(leadVisibility[(dashboardType+1)%3].name, leadStatus?.all, null, null, others.leadSource, null, null, null, false, [null, null], 'source')">
                                                            <div class="position-absolute h-100 top-0 left-0"
                                                                [style.width]="(others.count / allLeadsCount) * 100 + '%'"
                                                                [ngStyle]="{'background-color': others.progressColor || '#CDD5DB'}">
                                                            </div>
                                                            <div
                                                                class="flex-between position-absolute w-100 h-100 top-0 left-0 p-10">
                                                                <div class="align-center">
                                                                    <img [type]="'leadrat'"
                                                                        [appImage]="others.imageURL ? s3BucketUrl+others.imageURL : ''"
                                                                        alt="logo" width=14px height=14px class="mr-10">
                                                                    <div class="text-sm">{{
                                                                        LeadSource[others.leadSource] }}
                                                                    </div>
                                                                </div>
                                                                <h5>{{ others.count }}</h5>
                                                            </div>
                                                        </div>
                                                    </ng-container>
                                                </ng-container>
                                            </ng-container>
                                        </div>
                                    </ng-container>
                                </div>
                            </div>
                        </ng-container>
                        <!-- level 2 - Pipeline(Bar chart) -->
                        <!-- <div class="mt-12 bg-white br-2"
                        [ngClass]="expandPipeline? 'z-index-1001 position-fixed top-20 bottom-20 left-40 right-40':'w-100'">
                        <div class="p-6 flex-between border-bottom">
                            <div class="d-flex">
                                <div class="text-black-200 mx-4">Leads pipeline</div>
                                <div class="dot dot-sm bg-black-200 cursor-pointer"
                                    title="This Graph will give you a segregated count of Active leads which are in play categorized as new for new leads, In engagement for callbacks and meetings and site visit scheduled, site visit done, booked are represented as name. you can also filter this out based on source and date.">
                                    <span class="m-auto text-white">?</span>
                                </div>
                            </div>
                            <form [formGroup]="pipelineForm" class="d-flex">
                                <div class="align-center position-relative cursor-pointer d-flex mr-10">
                                    <span class="position-absolute left-6 z-index-2 text-xs text-black-200">
                                        Lead Source</span>
                                    <div class="show-hide-slate w-90" (click)="filters.showPipelineFilter = false">
                                        <ng-select [virtualScroll]="true" [items]="leadSources" [multiple]="true"
                                            [searchable]="false" [closeOnSelect]="false" (change)="onPipelineChange()"
                                            formControlName="pipelineSource">
                                            <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                let-index="index">
                                                <div class="checkbox-container"><input type="checkbox"
                                                        id="item-{{index}}" data-automate-id="item-{{index}}"
                                                        [checked]="item$.selected"><span
                                                        class="checkmark"></span>{{item}}</div>
                                            </ng-template>
                                        </ng-select>
                                    </div>
                                </div>
                                <div class="position-relative mr-10">
                                    <div class="flex-center bg-violet-600 p-6 cursor-pointer"
                                        (click)="onFilterClick('showPipelineFilter')">
                                        <span
                                            class="icon ic-calendar-minus  ic-xxs ic-black-200 cursor-pointer mr-4 ip-mr-10"></span>
                                        <span
                                            class="text-black-200 mr-12 text-xs text-nowrap ip-d-none">{{pipelineForm.controls['pipelineRange'].value}}</span>
                                        <span class="ic-triangle-down icon ic-black-200 ic-xx-xs"></span>
                                    </div>
                                    <div class="position-absolute top-30 w-270 bg-white right-0 z-index-1001 box-shadow-10"
                                        *ngIf="filters.showPipelineFilter">
                                        <div class="d-flex">
                                            <div class="w-35 bg-light-slate">
                                                <div
                                                    class="box-shadow-4 py-6 px-10 fw-semi-bold text-sm text-black-200">
                                                    Date Type
                                                </div>
                                                <ng-container *ngFor="let type of dateTypeFilterList">
                                                    <div class="form-check form-check-inline p-6">
                                                        <input type="radio" id="inpPipeline{{type.value}}"
                                                            name="pipelineDType" formControlName="pipelineDType"
                                                            [value]="type.value" class="radio-check-input w-8 h-8 mr-8">
                                                        <label class="text-dark-gray cursor-pointer text-large text-sm"
                                                            for="inpPipeline{{type.value}}">{{type.displayName}}</label>
                                                    </div>
                                                </ng-container>
                                            </div>
                                            <div class="w-65 bg-light-pearl">
                                                <div
                                                    class="box-shadow-4 py-6 px-10 fw-semi-bold text-sm text-black-200">
                                                    Range
                                                </div>
                                                <ng-container *ngFor="let type of dateFilterList">
                                                    <div class="form-check form-check-inline p-6">
                                                        <input type="radio" id="inpPipeline{{type.value}}"
                                                            name="pipelineRange" formControlName="pipelineRange"
                                                            [value]="type.value" class="radio-check-input w-8 h-8 mr-8">
                                                        <label class="text-dark-gray cursor-pointer text-large text-sm"
                                                            for="inpPipeline{{type.value}}">{{type.displayName}}</label>
                                                    </div>
                                                </ng-container>
                                                <div class="position-relative dashboard-filter form-group m-6 mb-16"
                                                    [ngClass]="{'pe-none disabled' : pipelineForm.controls['pipelineRange'].value !== 'Custom'}">
                                                    <form-errors-wrapper
                                                        [control]="pipelineForm.controls['pipelineDate']" label="Date">
                                                        <input type="text" readonly [owlDateTimeTrigger]="dt1"
                                                            [owlDateTime]="dt1" [selectMode]="'range'"
                                                            formControlName="pipelineDate" placeholder="Select date" />
                                                        <owl-date-time [pickerType]="'calendar'" #dt1></owl-date-time>
                                                    </form-errors-wrapper>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex-end p-6">
                                            <h6 class="fw-semi-bold text-black-200 text-decoration-underline mr-10 cursor-pointer"
                                                (click)="filters.showPipelineFilter = false">
                                                Cancel</h6>
                                            <div class="btn-coal" (click)="onPipelineChange()">Apply</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="bg-violet-600 p-12 cursor-pointer icon ic-black-200 ic-xxs"
                                    (click)="expandPipeline = !expandPipeline"
                                    [ngClass]="expandPipeline?' ic-collapse':'ic-expand'">
                                </div>
                            </form>
                        </div>
                        <ng-container *ngIf="!isPipelineLoading else skeletonLoader">
                            <ng-container *ngIf="leadBarChartConfig && expandPipeline">
                                <google-chart [type]="leadBarChartConfig.type" [data]="leadBarChartConfig.data"
                                    [columns]="leadBarChartConfig.columns" [options]="leadBarChartConfig.options"
                                    [style.width]="'100%'" [style.height]="'100%'"></google-chart>
                            </ng-container>
                            <ng-container *ngIf="leadBarChartConfig && !expandPipeline">
                                <google-chart [type]="leadBarChartConfig.type" [data]="leadBarChartConfig.data"
                                    [columns]="leadBarChartConfig.columns" [options]="leadBarChartConfig.options"
                                    [style.width]="'100%'" [style.height]="'100%'"></google-chart>
                            </ng-container>
                        </ng-container>
                    </div> -->
                        <!-- level 3 - Received -->
                        <div class="mt-12 bg-white br-2"
                            [ngClass]="expandReceived? 'z-index-1001 position-fixed top-20 bottom-20 left-40 right-40':'w-100'">
                            <div class="p-6 flex-between border-bottom">
                                <div class="d-flex">
                                    <div class="text-black-200 mx-4">Lead Received</div>
                                    <div class="dot dot-sm bg-black-200 cursor-pointer"
                                        title="This Graph Compares the leads received by integration based on source and user can Filter based on the date.">
                                        <span class="m-auto text-white">?</span>
                                    </div>
                                </div>
                                <form [formGroup]="receivedForm" class="d-flex">
                                    <div class="position-relative mr-10">
                                        <div class="flex-center bg-violet-600 p-6 cursor-pointer"
                                            (click)="onFilterClick('showReceivedFilter')">
                                            <span
                                                class="icon ic-calendar-minus  ic-xxs ic-black-200 cursor-pointer mr-4 ip-mr-10"></span>
                                            <span
                                                class="text-black-200 mr-12 text-xs text-nowrap ip-d-none">{{receivedForm.controls['receivedRange'].value
                                                + ' By ' + receivedForm.controls['receivedFrequency'].value}}</span>
                                            <span class="ic-triangle-down icon ic-black-200 ic-xx-xs"></span>
                                        </div>
                                        <div class="position-absolute top-30 w-330 bg-white ph-w-230px right-0 z-index-1001 box-shadow-10"
                                            *ngIf="filters.showReceivedFilter">
                                            <div class="d-flex ph-flex-col">
                                                <div class="w-30 ph-w-100 bg-light-slate">
                                                    <div
                                                        class="box-shadow-4 py-6 px-10 fw-semi-bold text-sm text-black-200">
                                                        Date Type
                                                    </div>
                                                    <div>
                                                        <ng-container *ngFor="let type of dateTypeFilterList">
                                                            <div class="form-check form-check-inline p-6">
                                                                <input type="radio" id="inpReceived{{type.value}}"
                                                                    name="receivedDType" formControlName="receivedDType"
                                                                    [value]="type.value"
                                                                    class="radio-check-input w-8 h-8 mr-8">
                                                                <label
                                                                    class="text-dark-gray cursor-pointer text-large text-sm"
                                                                    for="inpReceived{{type.value}}">{{type.displayName}}</label>
                                                            </div>
                                                        </ng-container>
                                                    </div>
                                                </div>
                                                <div class="d-flex w-70 ph-w-100">
                                                    <div class="w-50 ph-w-60 bg-light-pearl">
                                                        <div
                                                            class="box-shadow-4 py-6 px-10 fw-semi-bold text-sm text-black-200">
                                                            Range
                                                        </div>
                                                        <ng-container *ngFor="let type of dateFilterList">
                                                            <div class="form-check form-check-inline p-6">
                                                                <input type="radio" id="inpReceived{{type.value}}"
                                                                    name="receivedRange" formControlName="receivedRange"
                                                                    [value]="type.value"
                                                                    class="radio-check-input w-8 h-8 mr-8">
                                                                <label
                                                                    class="text-dark-gray cursor-pointer text-large text-sm"
                                                                    for="inpReceived{{type.value}}">{{type.displayName}}</label>
                                                            </div>
                                                        </ng-container>
                                                        <div class="position-relative dashboard-filter form-group m-6 mb-16"
                                                            [ngClass]="{'pe-none disabled' : receivedForm.controls['receivedRange'].value !== 'Custom'}">
                                                            <form-errors-wrapper
                                                                [control]="receivedForm.controls['receivedDate']"
                                                                label="Date">
                                                                <input type="text" readonly [owlDateTimeTrigger]="dt1"
                                                                    [owlDateTime]="dt1" [selectMode]="'range'"
                                                                    formControlName="receivedDate"
                                                                    placeholder="Select date" />
                                                                <owl-date-time [pickerType]="'calendar'"
                                                                    (afterPickerOpen)="onPickerOpened(currentDate)"
                                                                    #dt1></owl-date-time>
                                                            </form-errors-wrapper>
                                                        </div>
                                                    </div>
                                                    <div class="w-50 ph-w-40">
                                                        <div
                                                            class="box-shadow-4 py-6 px-10 fw-semi-bold text-sm text-black-200">
                                                            Frequency
                                                        </div>
                                                        <ng-container *ngFor="let type of receivedFrequencyList">
                                                            <div class="form-check form-check-inline p-6">
                                                                <input type="radio" id="inpReceived{{type}}"
                                                                    name="receivedFrequency"
                                                                    formControlName="receivedFrequency" [value]="type"
                                                                    class="radio-check-input w-8 h-8 mr-8">
                                                                <label
                                                                    class="text-dark-gray cursor-pointer text-large text-sm"
                                                                    for="inpReceived{{type}}">{{type}}</label>
                                                            </div>
                                                        </ng-container>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="flex-end p-6">
                                                <h6 class="fw-semi-bold text-black-200 text-decoration-underline mr-10 cursor-pointer"
                                                    (click)="filters.showReceivedFilter = false">
                                                    Cancel</h6>
                                                <div class="btn-coal" (click)="onReceivedChange()">Apply</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="bg-violet-600 p-12 cursor-pointer icon ic-black-200 ic-xxs"
                                        (click)="expandReceived= !expandReceived"
                                        [ngClass]="expandReceived?' ic-collapse':'ic-expand'">
                                    </div>
                                </form>
                            </div>
                            <div class="px-16">
                                <ng-container *ngIf="!isReceivedLoading else skeletonLoader">
                                    <ng-container *ngIf="!isEmptyObject(leadReceivedData) else noData">
                                        <ng-container *ngIf="expandReceived">
                                            <canvasjs-chart [options]="chartOptions?.receivedChartOptions"
                                                [styles]="{width: '100%', height:'350px'}"></canvasjs-chart>
                                        </ng-container>
                                        <ng-container *ngIf="!expandReceived">
                                            <canvasjs-chart [options]="chartOptions?.receivedChartOptions"
                                                [styles]="{width: '100%', height:'200px'}"></canvasjs-chart>
                                        </ng-container>
                                    </ng-container>
                                </ng-container>
                                <div class="border-bottom"></div>
                                <drag-scroll class="scrollbar scroll-hide">
                                    <div class="py-16 align-center"
                                        [ngClass]="expandReceived?'':'justify-content-between'">
                                        <ng-container
                                            *ngFor="let source of chartOptions.receivedChartOptions.data;let last = last">
                                            <div class="align-center ml-16">
                                                <div class="h-60" [style.border-right]="'2px solid' + source.color">
                                                </div>
                                                <div class="ml-6 mr-20">
                                                    <h5 class="text-nowrap fw-600">{{hoveredReceivedData[source.name] ?
                                                        hoveredReceivedData[source.name] :
                                                        hoveredReceivedData[LeadSource[source.name]]}}
                                                        enquires</h5>
                                                    <div class="fw-300 mt-6">{{hoveredReceivedData.label ?
                                                        hoveredReceivedData.label : 'total'}}</div>
                                                    <h6 class="mt-10 fw-600">{{source.name || 'others'}}</h6>
                                                </div>
                                            </div>
                                            <div class="border-right h-16" *ngIf="!last"></div>
                                        </ng-container>
                                    </div>
                                </drag-scroll>
                            </div>
                        </div>
                        <!-- level 4 - Call, Activity -->
                        <div class="d-flex tb-flex-col w-100 mt-12">
                            <div class="pr-10 w-50 tb-w-100 tb-pr-0">
                                <div class="br-2 bg-white h-352"
                                    [ngClass]="expandCall? 'z-index-1001 position-fixed top-20 bottom-20 left-40 right-40':''">
                                    <div class="p-6 flex-between border-bottom bg-white">
                                        <div class="d-flex text-black-200">
                                            <div class="mx-4">Calls</div>
                                            <!-- <h6 class="fw-700 mr-4">({{hoveredActivityData}})</h6> -->
                                            <div class="dot dot-sm bg-black-200 cursor-pointer"
                                                title="This Graph will give you the comparison of activity of calls done by the users.">
                                                <span class="m-auto text-white">?</span>
                                            </div>
                                        </div>
                                        <div class="d-flex">
                                            <div class="position-relative mr-10">
                                                <div class="flex-center bg-violet-600 p-6 cursor-pointer"
                                                    (click)="onFilterClick('showCallFilter')">
                                                    <span
                                                        class="icon ic-calendar-minus  ic-xxs ic-black-200 cursor-pointer mr-4 ip-mr-10"></span>
                                                    <span
                                                        class="text-black-200 mr-12 text-xs text-nowrap ip-d-none">{{callForm.controls['callRange'].value
                                                        + ' By ' + callForm.controls['callFrequency'].value}}</span>
                                                    <span class="ic-triangle-down icon ic-black-200 ic-xx-xs"></span>
                                                </div>
                                                <form [formGroup]="callForm"
                                                    class="position-absolute top-30 w-270 bg-white right-0 z-index-1001 box-shadow-10"
                                                    *ngIf="filters.showCallFilter">
                                                    <div class="d-flex">
                                                        <div class="w-65 bg-light-pearl">
                                                            <div
                                                                class="box-shadow-4 py-6 px-10 fw-semi-bold text-sm text-black-200">
                                                                Range
                                                            </div>
                                                            <ng-container *ngFor="let type of dateFilterList">
                                                                <div class="form-check form-check-inline p-6">
                                                                    <input type="radio" id="inpCall{{type.value}}"
                                                                        name="callRange" formControlName="callRange"
                                                                        [value]="type.value"
                                                                        class="radio-check-input w-8 h-8 mr-8">
                                                                    <label
                                                                        class="text-dark-gray cursor-pointer text-large text-sm"
                                                                        for="inpCall{{type.value}}">{{type.displayName}}</label>
                                                                </div>
                                                            </ng-container>
                                                            <div class="position-relative dashboard-filter form-group m-6 mb-16"
                                                                [ngClass]="{'pe-none disabled' : callForm.controls['callRange'].value !== 'Custom'}">
                                                                <form-errors-wrapper
                                                                    [control]="callForm.controls['callDate']"
                                                                    label="Date">
                                                                    <input type="text" readonly
                                                                        [owlDateTimeTrigger]="dt1" [owlDateTime]="dt1"
                                                                        [selectMode]="'range'"
                                                                        formControlName="callDate"
                                                                        placeholder="Select date" />
                                                                    <owl-date-time [pickerType]="'calendar'"
                                                                        (afterPickerOpen)="onPickerOpened(currentDate)"
                                                                        #dt1></owl-date-time>
                                                                </form-errors-wrapper>
                                                            </div>
                                                        </div>
                                                        <div class="w-35">
                                                            <div
                                                                class="box-shadow-4 py-6 px-10 fw-semi-bold text-sm text-black-200">
                                                                Frequency
                                                            </div>
                                                            <ng-container *ngFor="let type of callFrequencyList">
                                                                <div class="form-check form-check-inline p-6">
                                                                    <input type="radio" id="inpCall{{type}}"
                                                                        name="callFrequency"
                                                                        formControlName="callFrequency" [value]="type"
                                                                        class="radio-check-input w-8 h-8 mr-8">
                                                                    <label
                                                                        class="text-dark-gray cursor-pointer text-large text-sm"
                                                                        for="inpCall{{type}}">{{type}}</label>
                                                                </div>
                                                            </ng-container>
                                                        </div>
                                                    </div>
                                                    <div class="flex-end p-6">
                                                        <h6 class="fw-semi-bold text-black-200 text-decoration-underline mr-10 cursor-pointer"
                                                            (click)="filters.showCallFilter = false">
                                                            Cancel</h6>
                                                        <div class="btn-coal" (click)="onCallChange()">Apply</div>
                                                    </div>
                                                </form>
                                            </div>
                                            <div class="bg-violet-600 p-12 cursor-pointer icon ic-black-200 ic-xxs"
                                                (click)="expandCall = !expandCall"
                                                [ngClass]="expandCall?' ic-collapse':'ic-expand'">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="px-16">
                                        <div *ngIf="!isCallReportLoading else skeletonLoader" class="my-10">
                                            <ng-container *ngIf="!isEmptyObject(leadCallReportData) else noData">
                                                <ng-container *ngIf="expandCall">
                                                    <canvasjs-chart [options]="chartOptions?.callChartOptions"
                                                        [styles]="{width: '100%', height:'350px'}"></canvasjs-chart>
                                                </ng-container>
                                                <ng-container *ngIf="!expandCall">
                                                    <canvasjs-chart [options]="chartOptions?.callChartOptions"
                                                        [styles]="{width: '100%', height:'200px'}"></canvasjs-chart>
                                                </ng-container>
                                            </ng-container>
                                        </div>
                                        <div class="border-bottom"></div>
                                        <div class="mx-12">
                                            <drag-scroll class="scrollbar scroll-hide">
                                                <div class="py-16 align-center"
                                                    [ngClass]="expandCall?'':'justify-content-between'">
                                                    <div class="align-center">
                                                        <div class="h-60" style="border-right: 2px solid #0194E7;">
                                                        </div>
                                                        <div class="ml-6 mr-20">
                                                            <h5 class="text-nowrap fw-600"> {{hoveredCallData.dialed}}
                                                                calls
                                                            </h5>
                                                            <div class="fw-300 mt-6">{{hoveredCallData.label ?
                                                                hoveredCallData.label : 'total'}}</div>
                                                            <h6 class="mt-10 fw-600">dialed</h6>
                                                        </div>
                                                    </div>
                                                    <div class="border-right h-16"></div>
                                                    <div class="align-center ml-16">
                                                        <div class="h-60" style="border-right: 2px solid #9BEDFF;">
                                                        </div>
                                                        <div class="ml-6 mr-20">
                                                            <h5 class="text-nowrap fw-600"> {{hoveredCallData.incoming}}
                                                                calls</h5>
                                                            <div class="fw-300 mt-6">{{hoveredCallData.label ?
                                                                hoveredCallData.label : 'total'}}</div>
                                                            <h6 class="mt-10 fw-600">incoming</h6>
                                                        </div>
                                                    </div>
                                                    <div class="border-right h-16"></div>
                                                    <div class="align-center ml-16">
                                                        <div class="h-60" style="border-right: 2px solid #B7FE9E;">
                                                        </div>
                                                        <div class="ml-6 mr-20">
                                                            <h5 class="text-nowrap fw-600">
                                                                {{hoveredCallData.connected}}
                                                                calls</h5>
                                                            <div class="fw-300 mt-6">{{hoveredCallData.label ?
                                                                hoveredCallData.label : 'total'}}</div>
                                                            <h6 class="mt-10 fw-600">connected</h6>
                                                        </div>
                                                    </div>
                                                    <div class="border-right h-16"></div>
                                                    <div class="align-center ml-16">
                                                        <div class="h-60" style="border-right: 2px solid #FFCBCC;">
                                                        </div>
                                                        <div class="ml-6 mr-20">
                                                            <h5 class="text-nowrap fw-600">
                                                                {{hoveredCallData.notConnected}}
                                                                calls
                                                            </h5>
                                                            <div class="fw-300 mt-6">{{hoveredCallData.label ?
                                                                hoveredCallData.label : 'total'}}</div>
                                                            <h6 class="mt-10 fw-600 text-nowrap">not connected</h6>
                                                        </div>
                                                    </div>
                                                    <div class="border-right h-16"></div>
                                                    <div class="align-center ml-16">
                                                        <div class="h-60" style="border-right: 2px solid #B10000;">
                                                        </div>
                                                        <div class="ml-6 mr-20">
                                                            <h5 class="text-nowrap fw-600"> {{hoveredCallData.missed}}
                                                                calls</h5>
                                                            <div class="fw-300 mt-6">{{hoveredCallData.label ?
                                                                hoveredCallData.label : 'total'}}</div>
                                                            <h6 class="mt-10 fw-600">missed</h6>
                                                        </div>
                                                    </div>
                                                </div>
                                            </drag-scroll>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="tb-mt-10 bg-white br-2"
                                [ngClass]="expandActivity? 'z-index-1001 position-fixed top-20 bottom-20 left-40 right-40':'w-50 tb-w-100'">
                                <div class="p-6 flex-between border-bottom">
                                    <div class="d-flex">
                                        <div class="text-black-200 mx-4">Activity On Leads</div>
                                        <div class="dot dot-sm bg-black-200 cursor-pointer"
                                            title="This graph will represent the Communicational activity done on the leads. can be filtered based on source and lead dates.">
                                            <span class="m-auto text-white">?</span>
                                        </div>
                                    </div>
                                    <div class="d-flex">
                                        <div class="position-relative mr-10">
                                            <div class="flex-center bg-violet-600 p-6 cursor-pointer"
                                                (click)="onFilterClick('showActivityFilter')">
                                                <span
                                                    class="icon ic-calendar-minus  ic-xxs ic-black-200 cursor-pointer mr-4 ip-mr-10"></span>
                                                <span
                                                    class="text-black-200 mr-12 text-xs text-nowrap ip-d-none">{{activityForm.controls['activityRange'].value
                                                    + ' By ' + activityForm.controls['activityFrequency'].value}}</span>
                                                <span class="ic-triangle-down icon ic-black-200 ic-xx-xs"></span>
                                            </div>
                                            <form [formGroup]="activityForm"
                                                class="position-absolute top-30 w-330 bg-white right-0 z-index-1001 box-shadow-10 ph-w-230px"
                                                *ngIf="filters.showActivityFilter">
                                                <div class="d-flex ph-flex-col">
                                                    <div class="w-30 ph-w-100 bg-light-slate">
                                                        <div
                                                            class="box-shadow-4 py-6 px-10 fw-semi-bold text-sm text-black-200">
                                                            Date Type
                                                        </div>
                                                        <div>
                                                            <ng-container *ngFor="let type of dateTypeFilterList">
                                                                <div class="form-check form-check-inline p-6">
                                                                    <input type="radio" id="inpActivity{{type.value}}"
                                                                        name="activityDType"
                                                                        formControlName="activityDType"
                                                                        [value]="type.value"
                                                                        class="radio-check-input w-8 h-8 mr-8">
                                                                    <label
                                                                        class="text-dark-gray cursor-pointer text-large text-sm"
                                                                        for="inpActivity{{type.value}}">{{type.displayName}}</label>
                                                                </div>
                                                            </ng-container>
                                                        </div>
                                                    </div>
                                                    <div class="d-flex w-70 ph-w-100">
                                                        <div class="w-50 bg-light-pearl ph-w-60">
                                                            <div
                                                                class="box-shadow-4 py-6 px-10 fw-semi-bold text-sm text-black-200">
                                                                Range
                                                            </div>
                                                            <ng-container *ngFor="let type of dateFilterList">
                                                                <div class="form-check form-check-inline p-6">
                                                                    <input type="radio" id="inpActivity{{type.value}}"
                                                                        name="activityRange"
                                                                        formControlName="activityRange"
                                                                        [value]="type.value"
                                                                        class="radio-check-input w-8 h-8 mr-8">
                                                                    <label
                                                                        class="text-dark-gray cursor-pointer text-large text-sm"
                                                                        for="inpActivity{{type.value}}">{{type.displayName}}</label>
                                                                </div>
                                                            </ng-container>
                                                            <div class="position-relative dashboard-filter form-group m-6 mb-16"
                                                                [ngClass]="{'pe-none disabled' : activityForm.controls['activityRange'].value !== 'Custom'}">
                                                                <form-errors-wrapper
                                                                    [control]="activityForm.controls['activityDate']"
                                                                    label="Date">
                                                                    <input type="text" readonly
                                                                        [owlDateTimeTrigger]="dt1" [owlDateTime]="dt1"
                                                                        [selectMode]="'range'"
                                                                        formControlName="activityDate"
                                                                        placeholder="Select date" />
                                                                    <owl-date-time [pickerType]="'calendar'"
                                                                        (afterPickerOpen)="onPickerOpened(currentDate)"
                                                                        #dt1></owl-date-time>
                                                                </form-errors-wrapper>
                                                            </div>
                                                        </div>
                                                        <div class="w-50 ph-w-40">
                                                            <div
                                                                class="box-shadow-4 py-6 px-10 fw-semi-bold text-sm text-black-200">
                                                                Frequency
                                                            </div>
                                                            <ng-container *ngFor="let type of activityFrequencyList">
                                                                <div class="form-check form-check-inline p-6">
                                                                    <input type="radio" id="inpActivity{{type}}"
                                                                        name="activityFrequency"
                                                                        formControlName="activityFrequency"
                                                                        [value]="type"
                                                                        class="radio-check-input w-8 h-8 mr-8">
                                                                    <label
                                                                        class="text-dark-gray cursor-pointer text-large text-sm"
                                                                        for="inpActivity{{type}}">{{type}}</label>
                                                                </div>
                                                            </ng-container>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="flex-end p-6">
                                                    <h6 class="fw-semi-bold text-black-200 text-decoration-underline mr-10 cursor-pointer"
                                                        (click)="filters.showActivityFilter = false">
                                                        Cancel</h6>
                                                    <div class="btn-coal" (click)="onActivityChange()">Apply</div>
                                                </div>
                                            </form>
                                        </div>
                                        <div class="bg-violet-600 p-12 cursor-pointer icon ic-black-200 ic-xxs"
                                            (click)="expandActivity= !expandActivity"
                                            [ngClass]="expandActivity?' ic-collapse':'ic-expand'">
                                        </div>
                                    </div>
                                </div>
                                <div class="px-16">
                                    <div *ngIf="!isActivityLoading else skeletonLoader" class="my-10">
                                        <ng-container *ngIf="!isEmptyObject(leadActivityData) else noData">
                                            <ng-container *ngIf="expandActivity">
                                                <canvasjs-chart [options]="chartOptions?.activityChartOptions"
                                                    [styles]="{width: '100%', height:'350px'}"></canvasjs-chart>
                                            </ng-container>
                                            <ng-container *ngIf="!expandActivity">
                                                <canvasjs-chart [options]="chartOptions?.activityChartOptions"
                                                    [styles]="{width: '100%', height:'200px'}"></canvasjs-chart>
                                            </ng-container>
                                        </ng-container>
                                    </div>
                                    <div class="border-bottom"></div>
                                    <div class="mx-12">
                                        <drag-scroll class="scrollbar scroll-hide">
                                            <div class="py-16 align-center"
                                                [ngClass]="expandActivity?'':'justify-content-between'">
                                                <div class="align-center">
                                                    <div class="h-60" style="border-right: 2px solid #FFF3A6;"></div>
                                                    <div class="ml-6 mr-20">
                                                        <h5 class="text-nowrap fw-600"> {{hoveredActivityData.sms}}
                                                            ({{hoveredActivityData.smsLeadUniqueCount}}) enquires
                                                        </h5>
                                                        <div class="fw-300 mt-6">{{hoveredActivityData.label ?
                                                            hoveredActivityData.label : 'total'}}</div>
                                                        <h6 class="mt-10 fw-600">sms</h6>
                                                    </div>
                                                    <div class="border-right h-16"></div>
                                                </div>
                                                <div class="align-center ml-16">
                                                    <div class="h-60" style="border-right: 2px solid #5887FF;"></div>
                                                    <div class="ml-6 mr-20">
                                                        <h5 class="text-nowrap fw-600"> {{hoveredActivityData.call}}
                                                            ({{hoveredActivityData.callLeadUniqueCount}}) enquires
                                                        </h5>
                                                        <div class="fw-300 mt-6">{{hoveredActivityData.label ?
                                                            hoveredActivityData.label : 'total'}}</div>
                                                        <h6 class="mt-10 fw-600">calls</h6>
                                                    </div>
                                                </div>
                                                <div class="border-right h-16"></div>
                                                <div class="align-center ml-16">
                                                    <div class="h-60" style="border-right: 2px solid #A682FF;"></div>
                                                    <div class="ml-6 mr-20">
                                                        <h5 class="text-nowrap fw-600"> {{hoveredActivityData.email}}
                                                            ({{hoveredActivityData.emailLeadUniqueCount}}) enquires
                                                        </h5>
                                                        <div class="fw-300 mt-6">{{hoveredActivityData.label ?
                                                            hoveredActivityData.label : 'total'}}</div>
                                                        <h6 class="mt-10 fw-600">email</h6>
                                                    </div>
                                                </div>
                                                <div class="border-right h-16"></div>
                                                <div class="align-center ml-16">
                                                    <div class="h-60" style="border-right: 2px solid #38DAB8;"></div>
                                                    <div class="ml-6 mr-20">
                                                        <h5 class="text-nowrap fw-600"> {{hoveredActivityData.whatsApp}}
                                                            ({{hoveredActivityData.whatsAppLeadUniqueCount}}) enquires
                                                        </h5>
                                                        <div class="fw-300 mt-6">{{hoveredActivityData.label ?
                                                            hoveredActivityData.label : 'total'}}</div>
                                                        <h6 class="mt-10 fw-600">whatsapp</h6>
                                                    </div>
                                                </div>
                                            </div>
                                        </drag-scroll>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- level 5 - Meeting, Site Visit -->
                        <div class="w-100 d-flex tb-flex-col">
                            <div class="pr-10 w-50 tb-w-100 tb-pr-0">
                                <div class="mt-10 py-6 br-2 bg-white"
                                    [ngClass]="expandMeeting? 'z-index-1001 position-fixed top-20 bottom-20 left-40 right-40':''">
                                    <div class="p-6 flex-between border-bottom">
                                        <div class="d-flex">
                                            <div class="text-black-200">Meetings</div>
                                            <h6 class="fw-700 mx-4" title="Meeting Scheduled">
                                                ({{(hoveredMeetingData?.meetingDone +
                                                hoveredMeetingData?.meetingNotDone + hoveredMeetingData?.meetingOverdue)
                                                || 0}})</h6>
                                            <div class="dot dot-sm bg-black-200 cursor-pointer"
                                                title="this graph represents the Meeting which are completed, not completed and scheduled. User can filter the date based on dates. Unique Leads count represents the distinct leads of the Meeting.">
                                                <span class="m-auto text-white">?</span>
                                            </div>
                                        </div>
                                        <div class="d-flex">
                                            <div class="position-relative mr-10">
                                                <div class="flex-center bg-violet-600 p-6 cursor-pointer"
                                                    (click)="onFilterClick('showMeetingFilter')">
                                                    <span
                                                        class="icon ic-calendar-minus  ic-xxs ic-black-200 cursor-pointer mr-4 ip-mr-10"></span>
                                                    <span
                                                        class="text-black-200 mr-12 text-xs text-nowrap ip-d-none">{{meetingForm.controls['meetingRange'].value
                                                        + ' By ' +
                                                        meetingForm.controls['meetingFrequency'].value}}</span>
                                                    <span class="ic-triangle-down icon ic-black-200 ic-xx-xs"></span>
                                                </div>
                                                <form [formGroup]="meetingForm"
                                                    class="position-absolute top-30 w-330 bg-white right-0 z-index-1001 box-shadow-10 ph-w-230px"
                                                    *ngIf="filters.showMeetingFilter">
                                                    <div class="d-flex ph-flex-col">
                                                        <div class="w-30 ph-w-100 bg-light-slate">
                                                            <div
                                                                class="box-shadow-4 py-6 px-10 fw-semi-bold text-sm text-black-200">
                                                                Date Type
                                                            </div>
                                                            <ng-container *ngFor="let type of dateTypeFilterList">
                                                                <div class="form-check form-check-inline p-6">
                                                                    <input type="radio" id="inpmeeting{{type.value}}"
                                                                        name="meetingDType"
                                                                        formControlName="meetingDType"
                                                                        [value]="type.value"
                                                                        class="radio-check-input w-8 h-8 mr-8">
                                                                    <label
                                                                        class="text-dark-gray cursor-pointer text-large text-sm"
                                                                        for="inpmeeting{{type.value}}">{{type.displayName}}</label>
                                                                </div>
                                                            </ng-container>
                                                        </div>
                                                        <div class="d-flex w-70 ph-w-100">
                                                            <div class="w-50 ph-w-60 bg-light-pearl">
                                                                <div
                                                                    class="box-shadow-4 py-6 px-10 fw-semi-bold text-sm text-black-200">
                                                                    Range</div>
                                                                <ng-container *ngFor="let type of dateFilterList">
                                                                    <div class="form-check form-check-inline p-6">
                                                                        <input type="radio"
                                                                            id="inpmeeting{{type.value}}"
                                                                            name="meetingRange"
                                                                            formControlName="meetingRange"
                                                                            [value]="type.value"
                                                                            class="radio-check-input w-8 h-8 mr-8">
                                                                        <label
                                                                            class="text-dark-gray cursor-pointer text-large text-sm"
                                                                            for="inpmeeting{{type.value}}">{{type.displayName}}</label>
                                                                    </div>
                                                                </ng-container>
                                                                <div class="position-relative dashboard-filter form-group m-6 mb-16"
                                                                    [ngClass]="{'pe-none disabled' : meetingForm.controls['meetingRange'].value !== 'Custom'}">
                                                                    <form-errors-wrapper
                                                                        [control]="meetingForm.controls['meetingDate']"
                                                                        label="Date">
                                                                        <input type="text" readonly
                                                                            [owlDateTimeTrigger]="dt1"
                                                                            [owlDateTime]="dt1" [selectMode]="'range'"
                                                                            formControlName="meetingDate"
                                                                            placeholder="Select date" />
                                                                        <owl-date-time [pickerType]="'calendar'"
                                                                            (afterPickerOpen)="onPickerOpened(currentDate)"
                                                                            #dt1></owl-date-time>
                                                                    </form-errors-wrapper>
                                                                </div>
                                                            </div>
                                                            <div class="w-50 ph-w-40">
                                                                <div
                                                                    class="box-shadow-4 py-6 px-10 fw-semi-bold text-sm text-black-200">
                                                                    Frequency
                                                                </div>
                                                                <ng-container *ngFor="let type of meetingFrequencyList">
                                                                    <div class="form-check form-check-inline p-6">
                                                                        <input type="radio" id="inpmeeting{{type}}"
                                                                            name="meetingFrequency"
                                                                            formControlName="meetingFrequency"
                                                                            [value]="type"
                                                                            class="radio-check-input w-8 h-8 mr-8">
                                                                        <label
                                                                            class="text-dark-gray cursor-pointer text-large text-sm"
                                                                            for="inpmeeting{{type}}">{{type}}</label>
                                                                    </div>
                                                                </ng-container>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="flex-end p-6">
                                                        <h6 class="fw-semi-bold text-black-200 text-decoration-underline mr-10 cursor-pointer"
                                                            (click)="filters.showMeetingFilter = false">
                                                            Cancel</h6>
                                                        <div class="btn-coal" (click)="onMeetingChange()">Apply</div>
                                                    </div>
                                                </form>
                                            </div>
                                            <div class="bg-violet-600 p-12 cursor-pointer icon ic-black-200 ic-xxs"
                                                (click)="expandMeeting= !expandMeeting"
                                                [ngClass]="expandMeeting?' ic-collapse':'ic-expand'">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="px-12">
                                        <div *ngIf="!isMeetingLoading else skeletonLoader" class="my-10">
                                            <ng-container *ngIf="!isEmptyObject(meetingData) else noData">
                                                <ng-container *ngIf="expandMeeting">
                                                    <canvasjs-chart [options]="chartOptions?.meetingChartOptions"
                                                        [styles]="{width: '100%', height:'280px'}"></canvasjs-chart>
                                                </ng-container>
                                                <ng-container *ngIf="!expandMeeting">
                                                    <canvasjs-chart [options]="chartOptions?.meetingChartOptions"
                                                        [styles]="{width: '100%', height:'200px'}"></canvasjs-chart>
                                                </ng-container>
                                            </ng-container>
                                        </div>
                                        <div class="px-12 py-10 flex-between border-top">
                                            <div class="d-flex">
                                                <div class="text-black-200 text-nowrap mx-4">Scheduled Meetings</div>
                                                <!-- <div class="dot dot-sm bg-black-200 cursor-pointer">
                                                <span class="m-auto text-white">?</span>
                                            </div> -->
                                            </div>
                                            <h6 class="fw-semi-bold text-accent-green">
                                                {{hoveredMeetingData?.meetingScheduled || 0}}
                                            </h6>
                                            <!-- [ngClass]="hoveredMeetingData?.upcomingMeetingScheduled ? 'cursor-pointer' : 'pe-none'"
                                        (click)="onClickNavigateFromCount(leadVisibility[(dashboardType+1)%3].name, leadStatus?.active, leadStatus?.scheduled, null, null, 'All', 'Meetings', false, [null, null], meetingDate, meetingDType)" -->
                                        </div>
                                        <div class="mx-12 border-top">
                                            <drag-scroll class="scrollbar scroll-hide">
                                                <div class="py-16 align-center"
                                                    [ngClass]="expandMeeting?'':'justify-content-between'">
                                                    <div class="align-center">
                                                        <div class="h-60" style="border-right: 2px solid #FFDC00;">
                                                        </div>
                                                        <div class="ml-6 mr-20">
                                                            <h5 class="text-nowrap fw-600">
                                                                <span>{{hoveredMeetingData.meetingDone}}
                                                                </span><span>
                                                                    ({{hoveredMeetingData.meetingDoneUniqueCount}}
                                                                    Unique Leads)</span>
                                                            </h5>
                                                            <h6 class="mt-6 fw-300">{{hoveredMeetingData.label ?
                                                                hoveredMeetingData.label : 'total'}}</h6>
                                                            <div class="fw-600 mt-10">meeting done</div>
                                                        </div>
                                                    </div>
                                                    <div class="border-right h-16"></div>
                                                    <div class="align-center ml-16">
                                                        <div class="h-60" style="border-right: 2px solid #FF686B;">
                                                        </div>
                                                        <div class="ml-6 mr-20">
                                                            <h5 class="text-nowrap fw-600">
                                                                <span>{{hoveredMeetingData.meetingNotDone}}</span>
                                                                <span>({{hoveredMeetingData.meetingNotDoneUniqueCount}}
                                                                    Unique leads)</span>
                                                            </h5>
                                                            <h6 class="mt-6 fw-300">{{hoveredMeetingData.label ?
                                                                hoveredMeetingData.label : 'total'}}</h6>
                                                            <div class="fw-600 mt-10">meeting not done</div>
                                                        </div>
                                                    </div>
                                                    <div class="border-right h-16"></div>
                                                    <div class="align-center ml-16">
                                                        <div class="h-60" style="border-right: 2px solid #B10000;">
                                                        </div>
                                                        <div class="ml-6 mr-20">
                                                            <h5 class="text-nowrap fw-600">
                                                                {{hoveredMeetingData.meetingOverdue}}
                                                            </h5>
                                                            <h6 class="mt-6 fw-300">{{hoveredMeetingData.label ?
                                                                hoveredMeetingData.label : 'total'}}</h6>
                                                            <div class="fw-600 mt-10 text-nowrap">meeting overdue</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </drag-scroll>
                                        </div>
                                        <div class="mx-12 py-10 flex-between border-top">
                                            <div class="d-flex">
                                                <div class="text-black-200 mx-4">Upcoming Meetings</div>
                                                <!-- <div class="dot dot-sm bg-black-200 cursor-pointer">
                                                <span class="m-auto text-white">?</span>
                                            </div> -->
                                            </div>
                                            <h6 class="fw-semi-bold text-accent-green">
                                                {{hoveredMeetingData?.upcomingMeetingScheduled || 0}}
                                            </h6>
                                            <!-- [ngClass]="hoveredMeetingData?.upcomingMeetingScheduled ? 'cursor-pointer' : 'pe-none'"
                                        (click)="onClickNavigateFromCount(leadVisibility[(dashboardType+1)%3].name, leadStatus?.active, leadStatus?.scheduled, null, null, 'All', 'Meetings', false, [null, null], meetingDate, meetingDType)" -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="w-50 tb-w-100">
                                <div class="mt-10 py-6 br-2 bg-white"
                                    [ngClass]="expandSiteVisit? 'z-index-1001 position-fixed top-20 bottom-20 left-40 right-40':''">
                                    <div class="py-6 px-10 flex-between border-bottom">
                                        <div class="d-flex">
                                            <div class="text-black-200">Site Visits</div>
                                            <h6 class="fw-700 mx-4">({{(hoveredSVData?.siteVisitDone +
                                                hoveredSVData?.siteVisitNotDone + hoveredSVData?.siteVisitOverdue) ||
                                                0}})
                                            </h6>
                                            <div class="dot dot-sm bg-black-200 cursor-pointer"
                                                title="this graph represents the site visits which are completed and not completed and scheduled and user can filter the date based on dates. Unique Leads count represents the distinct leads of the site visits.">
                                                <span class="m-auto text-white">?</span>
                                            </div>
                                        </div>
                                        <div class="d-flex">
                                            <div class="position-relative mr-10">
                                                <div class="flex-center bg-violet-600 p-6 cursor-pointer"
                                                    (click)="onFilterClick('showSiteVisitFilter')">
                                                    <span
                                                        class="icon ic-calendar-minus  ic-xxs ic-black-200 cursor-pointer mr-4 ip-mr-10"></span>
                                                    <span
                                                        class="text-black-200 mr-12 text-xs text-nowrap ip-d-none">{{SVForm.controls['SVRange'].value
                                                        + ' By ' + SVForm.controls['SVFrequency'].value}}</span>
                                                    <span class="ic-triangle-down icon ic-black-200 ic-xx-xs"></span>
                                                </div>
                                                <form [formGroup]="SVForm"
                                                    class="position-absolute top-30 w-330 bg-white right-0 z-index-1001 box-shadow-10 ph-w-230px"
                                                    *ngIf="filters.showSiteVisitFilter">
                                                    <div class="d-flex ph-flex-col">
                                                        <div class="w-30 ph-w-100 bg-light-slate">
                                                            <div
                                                                class="box-shadow-4 py-6 px-10 fw-semi-bold text-sm text-black-200">
                                                                Date Type
                                                            </div>
                                                            <ng-container *ngFor="let type of dateTypeFilterList">
                                                                <div class="form-check form-check-inline p-6">
                                                                    <input type="radio" id="inpsiteVisit{{type.value}}"
                                                                        name="SVDType" formControlName="SVDType"
                                                                        [value]="type.value"
                                                                        class="radio-check-input w-8 h-8 mr-8">
                                                                    <label
                                                                        class="text-dark-gray cursor-pointer text-large text-sm"
                                                                        for="inpsiteVisit{{type.value}}">{{type.displayName}}</label>
                                                                </div>
                                                            </ng-container>
                                                        </div>
                                                        <div class="d-flex w-70 ph-w-100">
                                                            <div class="w-50 ph-w-60 bg-light-pearl">
                                                                <div
                                                                    class="box-shadow-4 py-6 px-10 fw-semi-bold text-sm text-black-200">
                                                                    Range
                                                                </div>
                                                                <ng-container *ngFor="let type of dateFilterList">
                                                                    <div class="form-check form-check-inline p-6">
                                                                        <input type="radio"
                                                                            id="inpsiteVisit{{type.value}}"
                                                                            name="SVRange" formControlName="SVRange"
                                                                            [value]="type.value"
                                                                            class="radio-check-input w-8 h-8 mr-8" />
                                                                        <label
                                                                            class="text-dark-gray cursor-pointer text-large text-sm"
                                                                            for="inpsiteVisit{{type.value}}">{{type.displayName}}</label>
                                                                    </div>
                                                                </ng-container>
                                                                <div class="position-relative dashboard-filter form-group m-6 mb-16"
                                                                    [ngClass]="{'pe-none disabled' : SVForm.controls['SVRange'].value !== 'Custom'}">
                                                                    <form-errors-wrapper
                                                                        [control]="SVForm.controls['SVDate']"
                                                                        label="Date">
                                                                        <input type="text" readonly
                                                                            [owlDateTimeTrigger]="dt1"
                                                                            [owlDateTime]="dt1" [selectMode]="'range'"
                                                                            formControlName="SVDate"
                                                                            placeholder="Select date" />
                                                                        <owl-date-time [pickerType]="'calendar'"
                                                                            (afterPickerOpen)="onPickerOpened(currentDate)"
                                                                            #dt1></owl-date-time>
                                                                    </form-errors-wrapper>
                                                                </div>
                                                            </div>
                                                            <div class="w-50 ph-w-40">
                                                                <div
                                                                    class="box-shadow-4 py-6 px-10 fw-semi-bold text-sm text-black-200">
                                                                    Frequency
                                                                </div>
                                                                <ng-container *ngFor="let type of SVFrequencyList">
                                                                    <div class="form-check form-check-inline p-6">
                                                                        <input type="radio" id="inpsiteVisit{{type}}"
                                                                            name="SVFrequency"
                                                                            formControlName="SVFrequency" [value]="type"
                                                                            class="radio-check-input w-8 h-8 mr-8" />
                                                                        <label
                                                                            class="text-dark-gray cursor-pointer text-large text-sm"
                                                                            for="inpsiteVisit{{type}}">{{type}}</label>
                                                                    </div>
                                                                </ng-container>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="flex-end p-6">
                                                        <h6 class="fw-semi-bold text-black-200 text-decoration-underline mr-10 cursor-pointer"
                                                            (click)="filters.showSiteVisitFilter = false">
                                                            Cancel</h6>
                                                        <div class="btn-coal" (click)="onSVChange()">Apply</div>
                                                    </div>
                                                </form>
                                            </div>
                                            <div class="bg-violet-600 p-12 cursor-pointer icon ic-black-200 ic-xxs"
                                                (click)="expandSiteVisit= !expandSiteVisit"
                                                [ngClass]="expandSiteVisit?' ic-collapse':'ic-expand'">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="px-12">
                                        <div *ngIf="!isSiteVisitLoading else skeletonLoader" class="my-10">
                                            <ng-container *ngIf="!isEmptyObject(siteVisitData) else noData">
                                                <ng-container *ngIf="expandSiteVisit">
                                                    <canvasjs-chart [options]="chartOptions?.SVChartOptions"
                                                        [styles]="{width: '100%', height:'280px'}"></canvasjs-chart>
                                                </ng-container>
                                                <ng-container *ngIf="!expandSiteVisit">
                                                    <canvasjs-chart [options]="chartOptions?.SVChartOptions"
                                                        [styles]="{width: '100%', height:'200px'}"></canvasjs-chart>
                                                </ng-container>
                                            </ng-container>
                                        </div>
                                        <div class="px-12 py-10 flex-between border-top">
                                            <div class="d-flex">
                                                <div class="text-black-200 text-nowrap mx-4">Scheduled Site Visits</div>
                                                <!-- <div class="dot dot-sm bg-black-200 cursor-pointer">
                                                <span class="m-auto text-white">?</span>
                                            </div> -->
                                            </div>
                                            <h6 class="fw-semi-bold text-accent-green">
                                                {{hoveredSVData?.siteVisitScheduled || 0}}</h6>
                                            <!-- [ngClass]="hoveredSVData?.upcomingSiteVisitScheduled ? 'cursor-pointer' : 'pe-none'"
                                            (click)="onClickNavigateFromCount(leadVisibility[(dashboardType+1)%3].name, leadStatus?.active, leadStatus?.scheduled, null, null, 'All', 'Site visits', false, [null, null], siteVisitDate, siteVisitDType)" -->
                                        </div>
                                        <div class="mx-12 border-top">
                                            <drag-scroll class="scrollbar scroll-hide">
                                                <div class="py-16 align-center"
                                                    [ngClass]="expandSiteVisit?'':'justify-content-between'">
                                                    <div class="align-center">
                                                        <div class="h-60" style="border-right: 2px solid #38DAB8;">
                                                        </div>
                                                        <div class="ml-6 mr-20">
                                                            <h5 class="text-nowrap fw-600">
                                                                <span>{{hoveredSVData.siteVisitDone}}
                                                                </span><span>
                                                                    ({{hoveredSVData.siteVisitDoneUniqueCount}}
                                                                    Unique Leads)</span>
                                                            </h5>
                                                            <h6 class="mt-6 fw-300">{{hoveredSVData.label ?
                                                                hoveredSVData.label : 'total'}}</h6>
                                                            <div class="fw-600 mt-10">site visit done</div>
                                                        </div>
                                                    </div>
                                                    <div class="border-right h-16"></div>
                                                    <div class="align-center ml-16">
                                                        <div class="h-60" style="border-right: 2px solid #FF686B;">
                                                        </div>
                                                        <div class="ml-6 mr-20">
                                                            <h5 class="text-nowrap fw-600">
                                                                <span>{{hoveredSVData.siteVisitNotDone}}</span>
                                                                <span>({{hoveredSVData.siteVisitNotDoneUniqueCount}}
                                                                    Unique
                                                                    Leads)</span>
                                                            </h5>
                                                            <h6 class="mt-6 fw-300">{{hoveredSVData.label ?
                                                                hoveredSVData.label : 'total'}}</h6>
                                                            <div class="fw-600 mt-10">site visit not done</div>
                                                        </div>
                                                    </div>
                                                    <div class="border-right h-16"></div>
                                                    <div class="align-center ml-16">
                                                        <div class="h-60" style="border-right: 2px solid #BC9BD6;">
                                                        </div>
                                                        <div class="ml-6 mr-20">
                                                            <h5 class="text-nowrap fw-600">
                                                                {{hoveredSVData.siteVisitOverdue}}</h5>
                                                            <h6 class="mt-6 fw-300">{{hoveredSVData.label ?
                                                                hoveredSVData.label : 'total'}}</h6>
                                                            <div class="fw-600 mt-10 text-nowrap">site visit overdue
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </drag-scroll>
                                        </div>
                                        <div class="mx-12 py-10 flex-between border-top">
                                            <div class="d-flex">
                                                <div class="text-black-200 text-nowrap mx-4">Upcoming Site Visits</div>
                                                <!-- <div class="dot dot-sm bg-black-200 cursor-pointer">
                                                <span class="m-auto text-white">?</span>
                                            </div> -->
                                            </div>
                                            <h6 class="fw-semi-bold text-accent-green">
                                                {{hoveredSVData?.upcomingSiteVisitScheduled || 0}}</h6>
                                            <!-- [ngClass]="hoveredSVData?.upcomingSiteVisitScheduled ? 'cursor-pointer' : 'pe-none'"
                                            (click)="onClickNavigateFromCount(leadVisibility[(dashboardType+1)%3].name, leadStatus?.active, leadStatus?.scheduled, null, null, 'All', 'Site visits', false, [null, null], siteVisitDate, siteVisitDType)" -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Lead Reports -->
                        <div class="border-white mt-10 bg-white br-2 w-100">
                            <div class="p-6 flex-between border-bottom">
                                <div class="d-flex">
                                    <div class="text-black-200 mx-4">Leads Report</div>
                                    <div class="dot dot-sm bg-black-200 cursor-pointer"
                                        title="This grid will give you a brief understanding about your users and their leads current status, can be filtered based on source and dates of leads.">
                                        <span class="m-auto text-white">?</span>
                                    </div>
                                </div>
                                <form class="d-flex position-relative" [formGroup]="leadReportForm">
                                    <div class="align-center position-relative cursor-pointer d-flex mr-10">
                                        <span class="position-absolute left-6 z-index-2 text-xs text-black-200">
                                            Lead Source</span>
                                        <div class="show-hide-slate w-90"
                                            (click)="filters.showLeadReportFilter = false">
                                            <ng-select [virtualScroll]="true" [items]="leadSources" [multiple]="true"
                                                [ngClass]="{'blinking pe-none': isSourcesLoading}" [searchable]="false"
                                                [closeOnSelect]="false" ResizableDropdown bindLabel="displayName"
                                                bindValue="displayName" (change)="onLeadReportChange()"
                                                formControlName="leadReportSource">
                                                <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                    let-index="index">
                                                    <div [title]="item" class="checkbox-container"><input
                                                            type="checkbox" id="item-{{index}}"
                                                            data-automate-id="item-{{index}}"
                                                            [checked]="item$.selected"><span
                                                            class="checkmark"></span>{{item?.displayName}}</div>
                                                </ng-template>
                                            </ng-select>
                                        </div>
                                    </div>
                                    <div class="flex-center bg-violet-600 p-6 cursor-pointer"
                                        (click)="onFilterClick('showLeadReportFilter')">
                                        <span
                                            class="icon ic-calendar-minus  ic-xxs ic-black-200 cursor-pointer mr-4"></span>
                                        <span
                                            class="text-black-200 mr-12 text-xs text-nowrap ip-d-none">{{leadReportForm.controls['leadReportRange'].value}}</span>
                                        <span class="ic-triangle-down icon ic-black-200 ic-xx-xs"></span>
                                    </div>
                                    <div class="position-absolute top-30 w-270 bg-white right-0 z-index-1001"
                                        *ngIf="filters.showLeadReportFilter">
                                        <div class="d-flex">
                                            <div class="w-35 bg-light-slate">
                                                <div
                                                    class="box-shadow-4 py-6 px-10 fw-semi-bold text-sm text-black-200">
                                                    Date Type
                                                </div>
                                                <div>
                                                    <ng-container *ngFor="let type of dateTypeFilterList">
                                                        <div class="form-check form-check-inline p-6">
                                                            <input type="radio" id="inpleadReport{{type.value}}"
                                                                name="leadReportDType" formControlName="leadReportDType"
                                                                [value]="type.value"
                                                                class="radio-check-input w-8 h-8 mr-8">
                                                            <label
                                                                class="text-dark-gray cursor-pointer text-large text-sm"
                                                                for="inpleadReport{{type.value}}">{{type.displayName}}</label>
                                                        </div>
                                                    </ng-container>
                                                </div>
                                            </div>
                                            <div class="w-65 bg-light-pearl">
                                                <div
                                                    class="box-shadow-4 py-6 px-10 fw-semi-bold text-sm text-black-200">
                                                    Range
                                                </div>
                                                <ng-container *ngFor="let type of dateFilterList">
                                                    <div class="form-check form-check-inline p-6">
                                                        <input type="radio" id="inpleadReport{{type.value}}"
                                                            name="leadReportRange" formControlName="leadReportRange"
                                                            [value]="type.value" class="radio-check-input w-8 h-8 mr-8">
                                                        <label class="text-dark-gray cursor-pointer text-large text-sm"
                                                            for="inpleadReport{{type.value}}">{{type.displayName}}</label>
                                                    </div>
                                                </ng-container>
                                                <div class="position-relative dashboard-filter form-group m-6 mb-16"
                                                    [ngClass]="{'pe-none disabled' : leadReportForm.controls['leadReportRange'].value !== 'Custom'}">
                                                    <form-errors-wrapper
                                                        [control]="leadReportForm.controls['leadReportDate']"
                                                        label="Date">
                                                        <input type="text" readonly [owlDateTimeTrigger]="dt1"
                                                            [owlDateTime]="dt1" [selectMode]="'range'"
                                                            formControlName="leadReportDate"
                                                            placeholder="Select date" />
                                                        <owl-date-time [pickerType]="'calendar'" #dt1
                                                            (afterPickerOpen)="onPickerOpened(currentDate)"></owl-date-time>
                                                    </form-errors-wrapper>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex-end p-6">
                                            <h6 class="fw-semi-bold text-black-200 text-decoration-underline mr-10 cursor-pointer"
                                                (click)="filters.showLeadReportFilter = false">
                                                Cancel</h6>
                                            <div class="btn-coal" (click)="onLeadReportChange()">Apply</div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="align-center bg-white w-100 px-10 no-validation border-bottom">
                                <span class="search icon ic-search ic-sm ic-slate-90 mr-12"> </span>
                                <input placeholder="type agent name" name="search"
                                    class="border-0 outline-0 w-100 py-12" (input)="onSearchByAgent('Report')"
                                    [(ngModel)]="searchTermReport" autocomplete="off">
                            </div>
                            <ng-container *ngIf="!isLeadReportsLoading else spinLoader">
                                <div class="scrollbar table-scrollbar tb-max-w-unset tb-w-100-66 ph-w-100-45"
                                    [ngClass]="showLeftNav ? 'max-w-100-442' : 'max-w-100-342'">
                                    <table class="table standard-table no-vertical-border">
                                        <thead>
                                            <tr class="text-nowrap">
                                                <th class="w-180">{{'DASHBOARD.agent' | translate}}
                                                    {{ 'GLOBAL.name' | translate}}</th>
                                                <th class="w-60">{{'DASHBOARD.total' | translate}}</th>
                                                <th class="w-55px">{{'GLOBAL.new' | translate }}</th>
                                                <th class="w-75px">{{'DASHBOARD.pending' | translate }}</th>
                                                <th class="w-75px">{{'GLOBAL.overdue' | translate }}</th>
                                                <th class="w-140">Expression Of Interest</th>
                                                <th class="w-85">{{'DASHBOARD.callbacks' | translate }}</th>
                                                <th class="w-135">{{'DASHBOARD.meetings-scheduled' | translate}}</th>
                                                <th class="w-140">{{'DASHBOARD.site-visits-scheduled' | translate}}</th>
                                                <th class="w-75px">{{'DASHBOARD.booked' | translate }}</th>
                                                <th class="w-110">Booking Cancel</th>
                                                <th class="w-110">{{'GLOBAL.not-interested' | translate }}</th>
                                                <th class="w-75px">{{'LEADS.dropped' | translate }}</th>
                                            </tr>
                                        </thead>
                                        <tbody
                                            class="text-secondary fw-semi-bold scrollbar scroll-hide max-h-100-400 min-h-48">
                                            <ng-container *ngIf="leadReports.length > 0; else noDataFound">
                                                <tr *ngFor="let report of leadReports">
                                                    <td class="w-180">
                                                        <div class="text-truncate-1 break-all">{{report?.firstName}}
                                                            {{report?.lastName}}</div>
                                                    </td>
                                                    <td class="w-60"
                                                        [ngClass]="report?.total && canViewLead ? 'cursor-pointer' : 'pe-none'"
                                                        (click)="onClickNavigateFromCount(leadVisibility[0].name, leadStatus?.all, null, report?.userId, null, null, null, null, true, [null, null], 'leadReport')">
                                                        {{report?.total || '--'}}</td>
                                                    <td class="w-55px"
                                                        [ngClass]="report?.new && canViewLead ? 'cursor-pointer' : 'pe-none'"
                                                        (click)="onClickNavigateFromCount(leadVisibility[0].name, leadStatus?.active, leadStatus?.new, report?.userId, null, null, null, null, true, [null, null], 'leadReport')">
                                                        {{report?.new || '--'}}</td>
                                                    <td class="w-75px"
                                                        [ngClass]="report?.pending && canViewLead ? 'cursor-pointer' : 'pe-none'"
                                                        (click)="onClickNavigateFromCount(leadVisibility[0].name, leadStatus?.active, leadStatus?.pending, report?.userId, null, null, null, null, true, [null, null], 'leadReport')">
                                                        {{report?.pending || '--'}}</td>
                                                    <td class="w-75px"
                                                        [ngClass]="report?.overdue && canViewLead ? 'cursor-pointer' : 'pe-none'"
                                                        (click)="onClickNavigateFromCount(leadVisibility[0].name, leadStatus?.active, leadStatus?.overdue, report?.userId, null, null, null, null, true, [null, null], 'leadReport')">
                                                        {{report?.overdue || '--'}}</td>
                                                    <td class="w-140"
                                                        [ngClass]="report?.expressionOfInterest && canViewLead ? 'cursor-pointer' : 'pe-none'"
                                                        (click)="onClickNavigateFromCount(leadVisibility[0].name, leadStatus?.active, leadStatus?.expressionOfInterestLeadCount, report?.userId, null, null, null, null, true, [null, null], 'leadReport')">
                                                        {{report?.expressionOfInterest || '--'}}</td>
                                                    <td class="w-85"
                                                        [ngClass]="report?.callback && canViewLead ? 'cursor-pointer' : 'pe-none'"
                                                        (click)="onClickNavigateFromCount(leadVisibility[0].name, leadStatus?.active, 'Callback', report?.userId, null, 'All', 'Callbacks', null, true, [null, null], 'leadReport')">
                                                        {{report?.callback || '--'}}</td>
                                                    <td class="w-135">
                                                        <div class="flex-between"><span
                                                                [ngClass]="report?.meetingScheduled && canViewLead ? 'cursor-pointer' : 'pe-none'"
                                                                (click)="onClickNavigateFromCount(leadVisibility[0].name, leadStatus?.active, 'Meeting Scheduled', report?.userId, null, 'All', 'Meetings', null, true, [null, null], 'leadReport')">
                                                                {{report?.meetingScheduled || '--'}}</span>
                                                            <span *ngIf="report?.meetingsDone"
                                                                [title]="'Meeting Done : ' + report?.meetingsDone"
                                                                class="ml-4 p-4 br-4 bg-green-70 text-nowrap text-white text-sm fw-semi-bold">MD:
                                                                <span class="fw-700">{{report?.meetingsDone}}
                                                                    ({{report?.meetingsDoneUnique ||
                                                                    '--'}})</span></span>
                                                        </div>
                                                    </td>
                                                    <td class="w-140">
                                                        <div class="flex-between"><span
                                                                [ngClass]="report?.siteVisitScheduled && canViewLead ? 'cursor-pointer' : 'pe-none'"
                                                                (click)="onClickNavigateFromCount(leadVisibility[0].name, leadStatus?.active, 'Site Visit Scheduled', report?.userId, null, 'All', 'Site visits', null, true, [null, null], 'leadReport')">
                                                                {{report?.siteVisitScheduled || '--'}}</span>
                                                            <span *ngIf="report?.siteVisitsDone"
                                                                [title]="'Site Visits Done : ' + report?.siteVisitsDone"
                                                                class="ml-4 p-4 br-4 bg-green-70 text-nowrap text-white text-sm fw-semi-bold">SVD:
                                                                <span class="fw-700"> {{report?.siteVisitsDone}}
                                                                    ({{report?.siteVisitsDoneUnique|| '--'}})</span>
                                                            </span>
                                                        </div>
                                                    </td>
                                                    <td class="w-75px text-accent-green"
                                                        [ngClass]="report?.booked && canViewLead ? 'cursor-pointer' : 'pe-none'"
                                                        (click)="onClickNavigateFromCount(leadVisibility[0].name, leadStatus?.booked, null, report?.userId, null, null, null, null, true, [null, null], 'leadReport')">
                                                        {{report.booked || '--'}}</td>
                                                    <td class="w-110"
                                                        [ngClass]="report?.bookingCancel && canViewLead ? 'cursor-pointer' : 'pe-none'"
                                                        (click)="onClickNavigateFromCount(leadVisibility[0].name, leadStatus?.bookingCancel, null, report?.userId, null, null, null, null, true, [null, null], 'leadReport')">
                                                        {{report.bookingCancel || '--'}}</td>
                                                    <td class="w-110"
                                                        [ngClass]="report?.notInterested && canViewLead ? 'cursor-pointer' : 'pe-none'"
                                                        (click)="onClickNavigateFromCount(leadVisibility[0].name, leadStatus?.['not-interested'], null, report?.userId, null, null, null, null, true, [null, null], 'leadReport')">
                                                        {{report?.notInterested || '--'}}</td>
                                                    <td class="w-75px"
                                                        [ngClass]="report?.dropped && canViewLead ? 'cursor-pointer' : 'pe-none'"
                                                        (click)="onClickNavigateFromCount(leadVisibility[0].name, leadStatus?.dropped, null, report?.userId, null, null, null, null, true, [null, null], 'leadReport')">
                                                        {{report?.dropped || '--'}}</td>
                                                </tr>
                                            </ng-container>
                                        </tbody>
                                    </table>
                                </div>
                            </ng-container>
                        </div>
                        <!-- Team performance/overview -->
                        <div class="border-white mt-10 bg-white br-2 w-100 mb-20 position-relative">
                            <div class="p-6 flex-between border-bottom">
                                <div class="d-flex">
                                    <div class="text-black-200 mx-4">Team performance/overview</div>
                                    <div class="dot dot-sm bg-black-200 cursor-pointer"
                                        title="This grid will give you a brief performance report of your users based on number of leads assigned and current site visit, meetings done or bookings done, can be filtered based on dates and lead source.">
                                        <span class="m-auto text-white">?</span>
                                    </div>
                                </div>
                                <form [formGroup]="performanceForm" class="d-flex">
                                    <div class="align-center position-relative cursor-pointer d-flex mr-10">
                                        <span class="position-absolute left-6 z-index-2 text-xs text-black-200">
                                            Lead Source</span>
                                        <div class="show-hide-slate w-90"
                                            (click)="filters.showPerformanceFilter = false">
                                            <ng-select [virtualScroll]="true" [items]="leadSources" [multiple]="true"
                                                [searchable]="false" [closeOnSelect]="false" ResizableDropdown
                                                bindLabel="displayName" bindValue="displayName"
                                                [ngClass]="{'blinking pe-none': isSourcesLoading}"
                                                (change)="onPerformanceChange()" formControlName="performanceSource">
                                                <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                    let-index="index">
                                                    <div [title]="item" class="checkbox-container"><input
                                                            type="checkbox" id="item-{{index}}"
                                                            data-automate-id="item-{{index}}"
                                                            [checked]="item$.selected"><span
                                                            class="checkmark"></span>{{item?.displayName}}</div>
                                                </ng-template>
                                            </ng-select>
                                        </div>
                                    </div>
                                    <div class="position-relative">
                                        <div class="flex-center bg-violet-600 p-6 cursor-pointer"
                                            (click)="onFilterClick('showPerformanceFilter')">
                                            <span
                                                class="icon ic-calendar-minus  ic-xxs ic-black-200 cursor-pointer mr-4 ip-mr-10"></span>
                                            <span
                                                class="text-black-200 mr-12 text-xs text-nowrap ip-d-none">{{performanceForm.controls['performanceRange'].value}}</span>
                                            <span class="ic-triangle-down icon ic-black-200 ic-xx-xs"></span>
                                        </div>
                                        <div class="position-absolute ntop-110 w-270 bg-white right-75 ph-right-40 z-index-1001 box-shadow-10"
                                            *ngIf="filters.showPerformanceFilter">
                                            <div class="d-flex">
                                                <div class="w-35 bg-light-slate">
                                                    <div
                                                        class="box-shadow-4 py-6 px-10 fw-semi-bold text-sm text-black-200">
                                                        Date Type
                                                    </div>
                                                    <div>
                                                        <ng-container *ngFor="let type of dateTypeFilterList">
                                                            <div class="form-check form-check-inline p-6">
                                                                <input type="radio" id="inpPerformance{{type.value}}"
                                                                    name="performanceDType"
                                                                    formControlName="performanceDType"
                                                                    [value]="type.value"
                                                                    class="radio-check-input w-8 h-8 mr-8">
                                                                <label
                                                                    class="text-dark-gray cursor-pointer text-large text-sm"
                                                                    for="inpPerformance{{type.value}}">{{type.displayName}}</label>
                                                            </div>
                                                        </ng-container>
                                                    </div>
                                                </div>
                                                <div class="w-65 bg-light-pearl">
                                                    <div
                                                        class="box-shadow-4 py-6 px-10 fw-semi-bold text-sm text-black-200">
                                                        Range
                                                    </div>
                                                    <ng-container *ngFor="let type of dateFilterList">
                                                        <div class="form-check form-check-inline p-6">
                                                            <input type="radio" id="inpPerformance{{type.value}}"
                                                                name="performanceRange"
                                                                formControlName="performanceRange" [value]="type.value"
                                                                class="radio-check-input w-8 h-8 mr-8">
                                                            <label
                                                                class="text-dark-gray cursor-pointer text-large text-sm"
                                                                for="inpPerformance{{type.value}}">{{type.displayName}}</label>
                                                        </div>
                                                    </ng-container>
                                                    <div class="position-relative dashboard-filter form-group m-6 mb-16"
                                                        [ngClass]="{'pe-none disabled' : performanceForm.controls['performanceRange'].value !== 'Custom'}">
                                                        <form-errors-wrapper
                                                            [control]="performanceForm.controls['performanceDate']"
                                                            label="Date">
                                                            <input type="text" readonly [owlDateTimeTrigger]="dt1"
                                                                [owlDateTime]="dt1" [selectMode]="'range'"
                                                                name="performanceDate" formControlName="performanceDate"
                                                                [value]="type" placeholder="Select date" />
                                                            <owl-date-time [pickerType]="'calendar'"
                                                                (afterPickerOpen)="onPickerOpened(currentDate)"
                                                                #dt1></owl-date-time>
                                                        </form-errors-wrapper>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="flex-end p-6">
                                                <h6 class="fw-semi-bold text-black-200 text-decoration-underline mr-10 cursor-pointer"
                                                    (click)="filters.showPerformanceFilter = false">
                                                    Cancel</h6>
                                                <div class="btn-coal" (click)="onPerformanceChange()">Apply</div>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="align-center bg-white w-100 px-10 no-validation border-bottom">
                                <span class="search icon ic-search ic-sm ic-slate-90 mr-12"> </span>
                                <input placeholder="type agent name" name="search"
                                    class="border-0 outline-0 w-100 py-12" (input)="onSearchByAgent('Performance')"
                                    [(ngModel)]="searchTermPerformance" autocomplete="off">
                            </div>
                            <ng-container *ngIf="!isPerformanceLoading else spinLoader">
                                <div class="scrollbar table-scrollbar tb-max-w-unset tb-w-100-66 ph-w-100-45"
                                    [ngClass]="showLeftNav ? 'max-w-100-442' : 'max-w-100-342'">
                                    <table class="table standard-table no-vertical-border">
                                        <thead>
                                            <tr class="text-nowrap">
                                                <th class="w-180">{{'DASHBOARD.agent' | translate}}
                                                    {{ 'GLOBAL.name' | translate}}</th>
                                                <th class="w-100px">Assigned Leads</th>
                                                <th class="w-100px">Site Visit Done</th>
                                                <th class="w-95">Meeting Done</th>
                                                <th class="w-75px">Booked</th>
                                            </tr>
                                        </thead>
                                        <tbody
                                            class="text-secondary fw-semi-bold scrollbar max-h-100-400 min-h-48 scroll-hide">
                                            <ng-container *ngIf="teamsPerformanceData.length > 0; else noDataFound">
                                                <tr *ngFor="let data of teamsPerformanceData">
                                                    <td class="w-180">
                                                        <div class="text-truncate-1 break-all">{{data?.firstName}}
                                                            {{data?.lastName}}</div>
                                                    </td>
                                                    <td class="w-100px"
                                                        [ngClass]="data?.assignedLeads && canViewLead ? 'cursor-pointer': 'pe-none'"
                                                        (click)="onClickNavigateFromCount(leadVisibility[0].name, leadStatus?.all, null, data?.userId, null, null, null, null, true, [null, null], 'performance')">
                                                        {{data?.assignedLeads || '--'}}</td>
                                                    <td class="w-100px"
                                                        [ngClass]="data?.siteVisitsDone && canViewLead ? 'cursor-pointer': 'pe-none'"
                                                        (click)="onClickNavigateFromCount(leadVisibility[0].name, leadStatus?.all, null, data?.userId, null, null, null, 'Site Visit Done', true, [null, null], 'performance')">
                                                        {{data?.siteVisitsDoneEventCount || '--'}}
                                                        {{data?.siteVisitsDone ?
                                                        '('
                                                        + data?.siteVisitsDone + ')' : ''}} </td>
                                                    <td class="w-95"
                                                        [ngClass]="data?.meetingsDone && canViewLead ? 'cursor-pointer': 'pe-none'"
                                                        (click)="onClickNavigateFromCount(leadVisibility[0].name, leadStatus?.all, null, data?.userId, null, null, null, 'Meeting Done', true, [null, null], 'performance')">
                                                        {{data?.meetingsDoneEventCount || '--'}} {{data?.meetingsDone ?
                                                        '('
                                                        +
                                                        data?.meetingsDone + ')' : ''}}</td>
                                                    <td class="w-75px text-accent-green"
                                                        [ngClass]="data?.booked && canViewLead ? 'cursor-pointer': 'pe-none'"
                                                        (click)="onClickNavigateFromCount(leadVisibility[0].name, leadStatus?.booked, null, data?.userId, null, null, null, null, true, [null, null], 'performance')">
                                                        {{data?.booked || '--'}}</td>
                                                </tr>
                                            </ng-container>
                                        </tbody>
                                    </table>
                                </div>
                            </ng-container>
                            <!-- <div title="Scroll To Top"
                                class="dot dot-lg bg-accent-green position-absolute nright-20 nbottom-10 cursor-pointer shadow"
                                (click)="scrollToTop()">
                                <span class="icon ic-chevron-down ic-xxs mb-2 rotate-180"></span>
                            </div> -->
                        </div>
                        <!-- Top performance/overview -->
                        <!-- <div *ngIf="dashboardType"
                            class="border-white mt-10 bg-white br-2 w-100 mb-20 position-relative">
                            <div class="p-6 flex-between border-bottom ip-flex-between-unset">
                                <div class="d-flex">
                                    <div class="text-black-200 mx-4 text-nowrap">Top performance/overview</div>
                                    <div class="dot dot-sm bg-black-200 cursor-pointer mr-4"
                                        title="This grid will give you a brief performance report of your users based on number of leads assigned and current site visit, meetings done or bookings done, can be filtered based on dates and lead source.">
                                        <span class="m-auto text-white">?</span>
                                    </div>
                                </div>
                                <form [formGroup]="topPerformerForm" class="d-flex">
                                    <div class="d-flex flex-wrap">
                                        <div
                                            class="position-relative cursor-pointer align-center mr-10 mb-4 dashboard-input-hide">
                                            <span class="position-absolute left-6 z-index-2 text-xs text-black-200">
                                                Project</span>
                                            <div class="show-hide-slate w-70px"
                                                (click)="filters.showTopPerformerFilter = false">
                                                <ng-select [virtualScroll]="true" [items]="projectList" bindValue="name"
                                                    bindLabel="name" [multiple]="true" [searchable]="true"
                                                    [closeOnSelect]="false" ResizableDropdown
                                                    (change)="onTopPerformerChange()" formControlName="Projects">
                                                    <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                        let-index="index">
                                                        <div [title]="item?.name" class="checkbox-container"><input
                                                                type="checkbox" id="item-{{index}}"
                                                                data-automate-id="item-{{index}}"
                                                                [checked]="item$.selected"><span
                                                                class="checkmark"></span>{{item?.name}}</div>
                                                    </ng-template>
                                                </ng-select>
                                            </div>
                                        </div>
                                        <div
                                            class="align-center position-relative cursor-pointer d-flex mr-10 mb-4 dashboard-input-hide">
                                            <span class="position-absolute left-6 z-index-2 text-xs text-black-200">
                                                Property</span>
                                            <div class="show-hide-slate w-70px"
                                                (click)="filters.showTopPerformerFilter = false">
                                                <ng-select [virtualScroll]="true" [items]="propertyList"
                                                    bindValue="title" bindLabel="title" [multiple]="true"
                                                    [searchable]="true" [closeOnSelect]="false" ResizableDropdown
                                                    (change)="onTopPerformerChange()" formControlName="Properties">
                                                    <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                        let-index="index">
                                                        <div [title]="item?.title" class="checkbox-container"><input
                                                                type="checkbox" id="item-{{index}}"
                                                                data-automate-id="item-{{index}}"
                                                                [checked]="item$.selected"><span
                                                                class="checkmark"></span>{{item?.title}}</div>
                                                    </ng-template>
                                                </ng-select>
                                            </div>
                                        </div>
                                        <div
                                            class="align-center position-relative cursor-pointer d-flex mr-10 mb-4 dashboard-input-hide">
                                            <span class="position-absolute left-6 z-index-2 text-xs text-black-200">
                                                Lead Source</span>
                                            <div class="show-hide-slate w-85"
                                                (click)="filters.showTopPerformerFilter = false">
                                                <ng-select [virtualScroll]="true" [items]="leadSources"
                                                    [multiple]="true" [searchable]="true" [closeOnSelect]="false"
                                                    bindValue="item" bindLabel="item" ResizableDropdown
                                                    (change)="onTopPerformerChange()" formControlName="Sources">
                                                    <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                        let-index="index">
                                                        <div [title]="item" class="checkbox-container"><input
                                                                type="checkbox" id="item-{{index}}"
                                                                data-automate-id="item-{{index}}"
                                                                [checked]="item$.selected"><span
                                                                class="checkmark"></span>{{item}}</div>
                                                    </ng-template>
                                                </ng-select>
                                            </div>
                                        </div>
                                        <div
                                            class="align-center position-relative cursor-pointer d-flex mr-10 mb-4 dashboard-input-hide">
                                            <span class="position-absolute left-6 z-index-2 text-xs text-black-200">
                                                Lead Sub Source</span>
                                            <div class="show-hide-slate w-105"
                                                (click)="filters.showTopPerformerFilter = false">
                                                <ng-select [virtualScroll]="true" [items]="subSourceList"
                                                    bindValue="item" bindLabel="item" [multiple]="true"
                                                    [searchable]="true" [closeOnSelect]="false" ResizableDropdown
                                                    (change)="onTopPerformerChange()" formControlName="SubSources">
                                                    <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                        let-index="index">
                                                        <div [title]="item" class="checkbox-container"><input
                                                                type="checkbox" id="item-{{index}}"
                                                                data-automate-id="item-{{index}}"
                                                                [checked]="item$.selected"><span
                                                                class="checkmark"></span>{{item}}</div>
                                                    </ng-template>
                                                </ng-select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="position-relative">
                                        <div class="flex-center bg-violet-600 p-6 cursor-pointer"
                                            (click)="onFilterClick('showTopPerformerFilter')">
                                            <span
                                                class="icon ic-calendar-minus  ic-xxs ic-black-200 cursor-pointer mr-4 ip-mr-10"></span>
                                            <span
                                                class="text-black-200 mr-12 text-xs text-nowrap ip-d-none">{{topPerformerForm.controls['topPerformerRange']?.value}}</span>
                                            <span class="ic-triangle-down icon ic-black-200 ic-xx-xs"></span>
                                        </div>
                                        <div class="position-absolute ntop-110 w-270 bg-white right-75 ph-right-40 z-index-1001 box-shadow-10"
                                            *ngIf="filters.showTopPerformerFilter">
                                            <div class="d-flex">
                                                <div class="w-35 bg-light-slate">
                                                    <div
                                                        class="box-shadow-4 py-6 px-10 fw-semi-bold text-sm text-black-200">
                                                        Date Type
                                                    </div>
                                                    <div>
                                                        <ng-container *ngFor="let type of dateTypeFilterList">
                                                            <div class="form-check form-check-inline p-6">
                                                                <input type="radio" id="inpPerformance{{type.value}}"
                                                                    name="topPerformerDType"
                                                                    formControlName="topPerformerDType"
                                                                    [value]="type.value"
                                                                    class="radio-check-input w-8 h-8 mr-8">
                                                                <label
                                                                    class="text-dark-gray cursor-pointer text-large text-sm"
                                                                    for="inpPerformance{{type.value}}">{{type.displayName}}</label>
                                                            </div>
                                                        </ng-container>
                                                    </div>
                                                </div>
                                                <div class="w-65 bg-light-pearl">
                                                    <div
                                                        class="box-shadow-4 py-6 px-10 fw-semi-bold text-sm text-black-200">
                                                        Range
                                                    </div>
                                                    <ng-container *ngFor="let type of dateFilterList">
                                                        <div class="form-check form-check-inline p-6">
                                                            <input type="radio" id="inpPerformance{{type.value}}"
                                                                name="topPerformerRange"
                                                                formControlName="topPerformerRange" [value]="type.value"
                                                                class="radio-check-input w-8 h-8 mr-8">
                                                            <label
                                                                class="text-dark-gray cursor-pointer text-large text-sm"
                                                                for="inpPerformance{{type.value}}">{{type.displayName}}</label>
                                                        </div>
                                                    </ng-container>
                                                    <div class="position-relative dashboard-filter form-group m-6 mb-16"
                                                        [ngClass]="{'pe-none disabled' : topPerformerForm.controls['topPerformerRange'].value !== 'Custom'}">
                                                        <form-errors-wrapper
                                                            [control]="topPerformerForm.controls['topPerformerDate']"
                                                            label="Date">
                                                            <input type="text" readonly [owlDateTimeTrigger]="dt1"
                                                                [owlDateTime]="dt1" [selectMode]="'range'"
                                                                name="topPerformerDate"
                                                                formControlName="topPerformerDate" [value]="type"
                                                                placeholder="Select date" />
                                                            <owl-date-time [pickerType]="'calendar'"
                                                                (afterPickerOpen)="onPickerOpened(currentDate)"
                                                                #dt1></owl-date-time>
                                                        </form-errors-wrapper>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="flex-end p-6">
                                                <h6 class="fw-semi-bold text-black-200 text-decoration-underline mr-10 cursor-pointer"
                                                    (click)="filters.showTopPerformerFilter = false">
                                                    Cancel</h6>
                                                <div class="btn-coal" (click)="onTopPerformerChange()">Apply</div>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="align-center p-10 gap-4 bg-white w-100 px-10 no-validation border-bottom">
                                <div (click)="onRadioChange(1)" class="align-center gap-1">
                                    <div class="radio-button" [class.checked]="selectedFilter === 1"></div>
                                    <span class="cursor-pointer">Booked Leads</span>
                                </div>
                                <div (click)="onRadioChange(0)" class="align-center gap-1">
                                    <div class="radio-button" [class.checked]="selectedFilter === 0"></div>
                                    <span class="cursor-pointer">Site Visits</span>
                                </div>
                                <div (click)="onRadioChange(2)" class="align-center gap-1">
                                    <div class="radio-button" [class.checked]="selectedFilter === 2"></div>
                                    <span class="cursor-pointer">Total Brokerage</span>
                                </div>
                            </div>
                            <div *ngIf="showFilters" class="bg-white px-4 py-12 tb-w-100-66 w-100-455">
                                <ng-container>
                                    <div class="bg-secondary flex-between">
                                        <drag-scroll class="br-4 overflow-auto d-flex scroll-hide w-100">
                                            <div class="d-flex" *ngFor="let filter of topPerformanceFilter | keyvalue">
                                                <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap"
                                                    *ngFor="let value of getArrayOfFilters(filter.key, filter.value)">
                                                    {{topPerformerFiltersKeyLabel[filter.key] || filter.key}}: {{value}}
                                                    <span
                                                        class="icon ic-cancel ic-dark ic-x-xs cursor-pointer text-light-slate ml-4"
                                                        (click)="onRemoveFilter(filter.key, value)"></span>
                                                </div>
                                            </div>
                                        </drag-scroll>
                                        <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap cursor-pointer"
                                            (click)="reset();">{{'BUTTONS.clear' | translate}} {{'GLOBAL.all' |
                                            translate}}
                                        </div>
                                    </div>
                                </ng-container>
                            </div>
                            <ng-container *ngIf="!isPerformanceLoading else spinLoader">
                                <div class="scrollbar table-scrollbar tb-max-w-unset tb-w-100-66 ph-w-100-45"
                                    [ngClass]="showLeftNav ? 'max-w-100-442' : 'max-w-100-342'">
                                    <table class="table standard-table no-vertical-border">
                                        <thead>
                                            <tr class="text-nowrap">
                                                <th class="w-130">{{'DASHBOARD.agent' | translate}}
                                                    {{ 'GLOBAL.name' | translate}}</th>
                                                <th class="w-95">Assigned Leads</th>
                                                <th class="w-95">Site Visit Done</th>
                                                <th class="w-95">Booked Leads</th>
                                                <th class="w-130">{{ 'Total Brokerage (' + defaultCurrency + ')' }}</th>
                                                ////<th class="w-75px">Total Incentive Earned</th>
                                            </tr>
                                        </thead>
                                        <tbody
                                            class="text-secondary fw-semi-bold scrollbar max-h-100-400 min-h-48 scroll-hide">
                                            <ng-container *ngIf="teamsPerformanceData.length > 0; else noDataFound">
                                                <tr *ngFor="let data of topPerformerseList">
                                                    <td class="w-130">
                                                        <div class="text-truncate-1 break-all">{{data?.firstName}}
                                                            {{data?.lastName}}</div>
                                                    </td>
                                                    <td class="w-95"
                                                        [ngClass]="data?.assignedLeads && canViewLead ? 'cursor-pointer': 'pe-none'"
                                                        (click)="onClickNavigateFromCount(leadVisibility[0].name, leadStatus?.all, null, data?.userId, null, null, null, null, true, [null, null], 'topPerformance')">
                                                        {{data?.assignedLeads || '--'}}</td>
                                                    <td class="w-95"
                                                        [ngClass]="data?.siteVisitDoneCount && canViewLead ? 'cursor-pointer': 'pe-none'"
                                                        (click)="onClickNavigateFromCount(leadVisibility[0].name, leadStatus?.all, null, data?.userId, null, null, null, 'Site Visit Done', true, [null, null], 'topPerformance')">
                                                        {{data?.siteVisitDoneCount || '--'}}
                                                        {{data?.siteVisitDoneCount ?
                                                        '('
                                                        + data?.siteVisitDoneUniqueCount + ')' : ''}} </td>
                                                    <td class="w-95"
                                                        [ngClass]="data?.booked && canViewLead ? 'cursor-pointer': 'pe-none'"
                                                        (click)="onClickNavigateFromCount(leadVisibility[0].name, leadStatus?.booked, null, data?.userId, null, null, null,null, true, [null, null], 'topPerformance')">
                                                        {{data?.booked ? data?.booked : ''}}</td>
                                                    <td class="w-130 text-accent-green"
                                                        [ngClass]="data?.totalBrokerage && canViewLead ? 'pe-none': 'pe-none'"
                                                        (click)="onClickNavigateFromCount(leadVisibility[0].name, leadStatus?.totalBrokerage, null, data?.userId, null, null, null, null, true, [null, null], 'topPerformance')">
                                                        {{data?.totalBrokerage || '--'}} {{data?.totalBrokerage >0 ?
                                                        defaultCurrency : ''}}</td>
                                                </tr>
                                            </ng-container>
                                        </tbody>
                                    </table>
                                </div>
                            </ng-container>
                        </div> -->
                    </div>
                </div>
                <div class="position-relative">
                    <div class="bg-white shadow py-6 px-4 brtl-4 brbl-4 cursor-pointer flex-center flex-col my-16 ml-2 h-50px"
                        (click)="onFilterClick('showGlobalFilter')">
                        <div class="icon ic-xxs ic-black-200 ic-sliders"></div>
                        <div class="mt-4 text-sm text-black-200">filter</div>
                    </div>
                    <form [formGroup]="globalForm"
                        class="position-absolute top-70 w-330 bg-white right-0 z-index-1001 box-shadow-10"
                        *ngIf="filters.showGlobalFilter">
                        <div class="d-flex">
                            <div class="w-30 bg-light-slate">
                                <div class="box-shadow-4 py-6 px-10 fw-semi-bold text-sm text-black-200">Date Type
                                </div>
                                <div>
                                    <ng-container *ngFor="let type of dateTypeFilterList">
                                        <div class="form-check form-check-inline p-6">
                                            <input type="radio" id="inpShowData{{type.value}}" name="globalDType"
                                                formControlName="globalDType" [value]="type.value"
                                                class="radio-check-input w-8 h-8 mr-8">
                                            <label class="text-dark-gray cursor-pointer text-large text-sm"
                                                for="inpShowData{{type.value}}">{{type.displayName}}</label>
                                        </div>
                                    </ng-container>
                                </div>
                            </div>
                            <div class="w-50 bg-light-pearl">
                                <div class="box-shadow-4 py-6 px-10 fw-semi-bold text-sm text-black-200">Range
                                </div>
                                <ng-container *ngFor="let type of dateFilterList">
                                    <div class="form-check form-check-inline p-6">
                                        <input type="radio" id="inpShowData{{type.value}}" name="globalRange"
                                            formControlName="globalRange" [value]="type.value"
                                            class="radio-check-input w-8 h-8 mr-8">
                                        <label class="text-dark-gray cursor-pointer text-large text-sm"
                                            for="inpShowData{{type.value}}">{{type.displayName}}</label>
                                    </div>
                                </ng-container>
                                <div class="position-relative dashboard-filter form-group m-6 mb-16"
                                    [ngClass]="{'pe-none disabled' : globalForm.controls['globalRange'].value !== 'Custom'}">
                                    <form-errors-wrapper [control]="globalForm.controls['globalDate']" label="Date">
                                        <input type="text" readonly [owlDateTimeTrigger]="dt1" [owlDateTime]="dt1"
                                            [selectMode]="'range'" formControlName="globalDate"
                                            placeholder="Select date" />
                                        <owl-date-time [pickerType]="'calendar'" #dt1
                                            (afterPickerOpen)="onPickerOpened(currentDate)"></owl-date-time>
                                    </form-errors-wrapper>
                                </div>
                            </div>
                            <div class="w-25">
                                <div class="box-shadow-4 py-6 px-10 fw-semi-bold text-sm text-black-200">Frequency
                                </div>
                                <ng-container *ngFor="let type of globalFrequencyList">
                                    <div class="form-check form-check-inline p-6">
                                        <input type="radio" id="inpShowData{{type}}" name="globalFrequency"
                                            formControlName="globalFrequency" [value]="type"
                                            class="radio-check-input w-8 h-8 mr-8">
                                        <label class="text-dark-gray cursor-pointer text-large text-sm"
                                            for="inpShowData{{type}}">{{type}}</label>
                                    </div>
                                </ng-container>
                            </div>
                        </div>
                        <div class="flex-end p-6">
                            <h6 class="fw-semi-bold text-black-200 text-decoration-underline mr-10 cursor-pointer"
                                (click)="filters.showGlobalFilter = false">
                                Cancel</h6>
                            <div class="btn-coal" (click)="onGlobalChange()">Apply</div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <!-- Reminders -->
        <div *ngIf="showEvents || screen?.innerWidth > 1279"
            class="bg-dark h-100-46 min-w-250 max-w-250 tb-position-absolute tb-right-0">
            <div class="pl-10">
                <div class="flex-between">
                    <div class="fw-700 text-large text-white mt-12">{{'DASHBOARD.reminders' | translate}}</div>
                    <div class="icon ic-close-secondary ic-sm cursor-pointer d-none tb-d-block mr-10 mt-10"
                        (click)="showEvents = !showEvents"></div>
                </div>
                <div class="mt-16 reminder d-flex">
                    <div class="w-70px mr-10">
                        <ng-select [virtualScroll]="true" [items]="monthsList" [searchable]="false" [clearable]="false"
                            [(ngModel)]="months" (change)="updateDaysInMonth()" ResizableDropdown>
                        </ng-select>
                    </div>
                    <div class="w-70px">
                        <ng-select [virtualScroll]="true" [items]="yearsList" [searchable]="false" [clearable]="false"
                            [(ngModel)]="years" (change)="updateDaysInMonth()" ResizableDropdown>
                        </ng-select>
                    </div>
                </div>
                <drag-scroll class="mt-20 scrollbar scroll-hide text-large d-flex fw-semi-bold text-sm text-dark-110">
                    <ng-container *ngFor="let day of monthDays; let i = index">
                        <div class="mr-12 pt-12 cursor-pointer calender-date flex-center-col"
                            id="clkLeadsCurrent{{ weekday }}" [ngClass]="{'active': i === selectedDateIndex }"
                            data-automate-id="clkLeadsCurrent{{weekday}}"
                            (click)="onUpcomingEventsDateSelection(day, i)">
                            <div>{{ weekDaysList[day?.getDay()] }}</div>
                            <div class="text-center text-dark-gray mt-6">{{ day?.getDate() }}</div>
                            <span class="align-center my-4">
                                <ng-container *ngIf="!isReminderCountLoading else blinker">
                                    <ng-container *ngIf="remindersCountByDate[i]?.siteVisits"><span
                                            class="bg-green-40 dot dot-xxs mr-2"></span></ng-container>
                                    <ng-container *ngIf="remindersCountByDate[i]?.meetings"><span
                                            class="bg-orange-800 dot dot-xxs mr-2"></span></ng-container>
                                    <ng-container *ngIf="remindersCountByDate[i]?.callbacks"><span
                                            class="bg-blue-50 dot dot-xxs mr-2"></span></ng-container>
                                </ng-container>
                            </span>
                        </div>
                    </ng-container>
                </drag-scroll>
                <div class="mt-10 border-bottom-dark-600 mx-16"></div>
                <div class="d-flex">
                    <div class="flex-column text-sm mr-20">
                        <div class="mt-10 fw-700 text-white">
                            {{upcomingEventsCount?.siteVisits || 0}}</div>
                        <div class="mt-6 d-flex">
                            <div class="h-15px br-4 border-left-green-40 mr-4"></div>
                            <div class="text-dark-110 text-nowrap">{{'DASHBOARD.site-visits' | translate}}</div>
                        </div>
                    </div>
                    <div class="flex-column text-sm">
                        <div class="mt-10 fw-700 text-white">
                            {{upcomingEventsCount?.meetings || 0}}</div>
                        <div class="mt-6 d-flex mr-16">
                            <div class="h-15px br-4 border-left-orange-800 mr-4"></div>
                            <div class="text-dark-110">{{'DASHBOARD.meetings' | translate}}</div>
                        </div>
                    </div>
                    <div class="flex-column text-sm">
                        <div class="mt-10 fw-700 text-white">
                            {{upcomingEventsCount?.callbacks || 0}}</div>
                        <div class="mt-6 d-flex mr-16">
                            <div class="h-15px br-4 border-left-blue-50 mr-4"></div>
                            <div class="text-dark-110">{{'DASHBOARD.callbacks' | translate}}</div>
                        </div>
                    </div>
                </div>
                <ng-container *ngIf="!isUpcomingEventsLoading else squareLoader">
                    <ng-container *ngIf="!areEmptyUpcomingEvents else emptyVectorDark">
                        <div class="align-center">
                            <span class="fw-semi-bold text-xs text-white mt-20 mb-8">{{upcomingEvents[0]?.date}}</span>
                            <span class="border-bottom-dark-600 w-100px ml-20 mt-10"></span>
                        </div>
                        <div class="h-100-307 scrollbar ph-h-100-380">
                            <div class="align-center" *ngFor="let event of upcomingEvents">
                                <div class="align-center mt-12">
                                    <div class="flex-column w-80">
                                        <div class="fw-semi-bold text-xs text-accent-green text-nowrap w-50px">
                                            {{event?.time}}
                                        </div>
                                        <div class="text-xs text-white fw-600 text-truncate-2"
                                            [title]="event?.firstName+ ' '+ event?.lastName">
                                            {{event?.firstName+ ' '+ event?.lastName}}</div>
                                    </div>
                                    <div class="h-35px br-4 mx-10" [ngClass]="event?.className"></div>
                                </div>
                                <div class="flex-column mt-12 text-light-slate fw-semi-bold">
                                    <div class="text-xs">{{dashboardUpcomingEvents[event?.status] ?
                                        dashboardUpcomingEvents[event?.status] :
                                        event?.status}} {{('DASHBOARD.with' | translate)?.toLowerCase()}}</div>
                                    <div class="fw-600 text-white text-xs w-110 text-truncate"
                                        [title]="event?.leadName">
                                        {{event?.leadName}}</div>
                                    <div class="text-xxs">{{!hideSubstatus?.includes(event?.status) ?
                                        (dashboardUpcomingEvents[event?.subStatus] ?
                                        dashboardUpcomingEvents[event?.subStatus] :
                                        event?.subStatus) : ''}}</div>
                                </div>
                            </div>
                        </div>
                    </ng-container>
                </ng-container>
            </div>
        </div>
    </div>
    <ng-template #emptyVectorDark>
        <div class="mt-60">
            <div class="flex-center">
                <img src="assets/images/no-data-dark.svg" alt="No records found" />
            </div>
            <h4 class="text-center mt-10 text-white ip-text-center">{{'DASHBOARD.no-events-found' | translate}}</h4>
        </div>
    </ng-template>
    <ng-template #skeletonLoader>
        <div class="p-20">
            <div class="loader-shimmer-banner shimmer-animation"></div>
        </div>
    </ng-template>
    <ng-template #shimmerLoader>
        <ng-container *ngFor="let skeleton of [1,2,3,4,5,6]">
            <ng-container *ngTemplateOutlet="smoothSkeletonLoader"></ng-container>
        </ng-container>
    </ng-template>
    <ng-template #loader>
        <div class="container p-4">
            <ng-container *ngFor="let dot of [1,2,3]">
                <div class="dot-falling"></div>
            </ng-container>
        </div>
    </ng-template>
    <ng-template #loaderWhite>
        <div class="container p-4">
            <ng-container *ngFor="let dot of [1,2,3]">
                <div class="dot-falling dot-white"></div>
            </ng-container>
        </div>
    </ng-template>
    <ng-template #smoothSkeletonLoader>
        <div class="mb-6">
            <span class="icon icon__skeleton shimmer-multiple"></span>
        </div>
    </ng-template>
    <ng-template #squareLoader>
        <div class="position-relative loader mt-100" *ngFor="let skeleton of [1,2,3,4,5,6,7,8,9], let i =index">
            <div class="square" id="sq{{ i+1 }}"></div>
        </div>
    </ng-template>
    <ng-template #blinker>
        <div class="loader-blink">
            <ng-container *ngFor="let dot of [1,2,3]">
                <span></span>
            </ng-container>
        </div>
    </ng-template>
    <ng-template #spinLoader>
        <div class="spin-loader my-20"></div>
    </ng-template>
    <ng-template #noData>
        <div class="h-200 flex-center-col px-20">
            <h4 class="fw-600 mb-4">OOPS!</h4>
            <div class="text-center">No Data Available for Selected Criteria. Please try again with Different Filter
                Options.</div>
        </div>
    </ng-template>
    <ng-template #noDataFound>
        <tr>
            <td class="h-100 header-4 text-secondary">No Data Available for Selected Criteria. Please try again with
                Different Filter Options.</td>
        </tr>
    </ng-template>
    <ng-template #noAssociatedData>
        <div class="mt-6">No Data Available</div>
    </ng-template>
    <ng-template #noSourceData>
        <div class="flex-center-col w-100">
            <img src="assets/images/no-data-dark.svg" alt="No records found" />
            <h4 class="text-center mt-10">No Data Available</h4>
        </div>
    </ng-template>
</ng-container>