import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { Store } from '@ngrx/store';
import { AnimationOptions } from 'ngx-lottie';
import { takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import {
  convertUrlsToLinks,
  getBedsDisplay,
  getBHKDisplay,
  getBRDisplay,
  getTimeZoneDate,
  groupBy,
  istFormat,
} from 'src/app/core/utils/common.util';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { getUserBasicDetails } from 'src/app/reducers/teams/teams.reducer';

@Component({
  selector: 'data-history',
  templateUrl: './data-history.component.html',
})
export class DataHistoryComponent implements OnInit, OnChanges {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @Input() history: any;
  @Input() formDirty: boolean = false;
  prospectHistoryData: any;
  filteredHistoryList: any[];
  noDocument: AnimationOptions = { path: 'assets/animations/no-document.json' };
  istFormat = istFormat;
  getBHKDisplay = getBHKDisplay;
  getBRDisplay = getBRDisplay;
  getTimeZoneDate = getTimeZoneDate;
  getBedsDisplay = getBedsDisplay;
  userData: any;
  convertUrlsToLinks = convertUrlsToLinks;
  globalSettingsDetails: any;

  constructor(
    private _store: Store<AppState>,
    private sanitizer: DomSanitizer
  ) { }

  ngOnInit(): void {
    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
      });
    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.globalSettingsDetails = data;
      });
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes?.formDirty) {
      this.formDirty = changes?.formDirty?.currentValue;
    }
    if (changes?.history) {
      this.prospectHistoryData = Object.entries(this.history).map(
        (item: any) => {
          return {
            date: item[0],
            data: [Object.entries(groupBy(item[1], 'modifiedOn'))],
          };
        }
      );

      this.filteredHistoryList = this.prospectHistoryData;
    }
  }

  isValidDate(value: string): boolean {
    const date = new Date(value);
    return !isNaN(date.getTime());
  }

  jsonFormat(data: any) {
    return Object?.entries(JSON.parse(data));
  }

  retrieveMessageFromBackend(msg: any) {
    return this.sanitizer.bypassSecurityTrustHtml(msg.replace(/\n/g, '<br>'));
  }
}
