<ng-container *ngIf="canView">
    <div class="pt-12 pb-20 px-30 position-relative">
        <div class="flex-between align-center">
            <div class="pt-12 align-center">
                <div class="icon ic-chevron-left ic-xxs ic-coal cursor-pointer mr-16" routerLink='/global-config'></div>
                <span class="icon ic-setting-solid ic-sm ic-black mr-8"></span>
                <h5 class="fw-600">{{ 'SIDEBAR.module-attendance' | translate }} {{ 'GLOBAL.settings' | translate }}
                </h5>
            </div>
        </div>
        <form [formGroup]="attendanceSettingsForm">
            <div class="bg-white py-16 mt-12 br-6">
                <div class="pl-20 flex-between" [ngClass]="{' pb-8':attendanceSettingsForm.get('shiftTiming').value }">
                    <div>
                        <h5 class="fw-600">{{ 'SETTINGS.shift-timing' | translate }}</h5>
                        <h6 class="text-dark-gray pt-4">{{ 'SETTINGS.shift-description'| translate }}</h6>
                    </div>
                    <div class="align-center mr-50 ph-mr-20 ml-20">
                        <div class="text-xs mr-8">{{attendanceSettingsForm.get('shiftTiming').value == true ? 'on' :
                            'off'}}
                        </div>
                        <input (click)="canUpdate ? openConfirmModal(changePopup, 'shiftTiming') : ''" type="checkbox"
                            class="toggle-switch toggle-active-sold" formControlName="shiftTiming" id="chkshiftTiming"
                            name="shiftTiming" [ngClass]="{'pe-none' : !canUpdate}">
                        <label for="chkshiftTiming" class="switch-label" [ngClass]="{'pe-none' : !canUpdate}"></label>
                    </div>
                </div>
                <ng-container *ngIf="attendanceSettingsForm.get('shiftTiming').value">
                    <div class="bg-white px-20 py-10 mt-2 br-6 border-top"
                        [ngClass]="{'pe-none blinking' : shiftTimingIsLoading}">
                        <div [ngClass]="{'pe-none' : !canUpdate}" class="flex-between">
                            <h5 class="fw-600">{{ 'SETTINGS.shift-timing' | translate }}</h5>
                            <label class="checkbox-container mb-4">
                                <input type="checkbox" [ngClass]="{'pe-none' : !canUpdate}"
                                    (change)="canUpdate ? applyToAllDays($event) : ''">
                                <span class="checkmark"></span>Apply to all days
                            </label>
                        </div>
                        <div *ngFor="let day of days" class="border ip-flex-wrap px-10 align-center py-16 mt-12 br-4"
                            [ngClass]="attendanceSettingsForm.get(day?.id)?.value ? 'bg-secondary' : 'bg-white'">
                            <div class="align-center">
                                <input (change)="canUpdate ? onToggleChange(day) : ''" type="checkbox"
                                    class="toggle-switch toggle-active-sold" [id]="'chkshiftTiming-' + day.id"
                                    [ngModel]="attendanceSettingsForm.get(day?.id)?.value" [formControlName]="day?.id"
                                    [ngClass]="{'pe-none' : !canUpdate}">
                                <label [for]="'chkshiftTiming-' + day.id" class="switch-label"
                                    [ngClass]="{'pe-none' : !canUpdate}"></label>
                            </div>
                            <h5 class="ml-10 w-140">{{day?.name}}</h5>
                            <div *ngIf="attendanceSettingsForm.get(day?.id)?.value"
                                class="ip-mt-10 d-flex ph-flex-col ip-w-100">
                                <div class="pr-12 ph-pr-0 ip-w-50 ph-w-100">
                                    <form-errors-wrapper label="Shift time from"
                                        [control]="attendanceSettingsForm?.controls?.[day?.shiftFrom]"
                                        autocomplete="off">
                                        <input (click)="canUpdate ? isChangesMade = true : ''" class="px-12 py-10 br-4"
                                            type="text" readonly [formControlName]="day?.shiftFrom"
                                            [ngClass]="{'pe-none' : !canUpdate}" [owlDateTimeTrigger]="dtFrom"
                                            [owlDateTime]="dtFrom" placeholder="From" />
                                        <owl-date-time [pickerType]="'timer'" [hour12Timer]="true"
                                            [startAt]="attendanceSettingsForm?.controls?.[day?.shiftFrom]?.value ? null : currentDate"
                                            #dtFrom="owlDateTime"></owl-date-time>
                                    </form-errors-wrapper>
                                </div>
                                <div class="ph-mt-20 ip-w-50 ph-w-100">
                                    <form-errors-wrapper label="Shift time to"
                                        [control]="attendanceSettingsForm?.controls?.[day?.shiftTo]" autocomplete="off">
                                        <input (click)="canUpdate ? isChangesMade = true : ''"
                                            class="px-12 py-10 br-4 ph-w-100" type="text" readonly
                                            [formControlName]="day?.shiftTo" [ngClass]="{'pe-none' : !canUpdate}"
                                            [owlDateTimeTrigger]="dtTo" [owlDateTime]="dtTo" placeholder="To" />
                                        <owl-date-time [pickerType]="'timer'" [hour12Timer]="true"
                                            [startAt]="attendanceSettingsForm?.controls?.[day?.shiftTo]?.value ? null : currentDate"
                                            #dtTo="owlDateTime"></owl-date-time>
                                    </form-errors-wrapper>
                                </div>
                            </div>
                            <div *ngIf="!attendanceSettingsForm.get(day?.id).value"
                                class="border bg-secondary ip-mt-10 px-20 align-center py-12 mt-4 br-4 w-345 ph-w-100 ph-flex-grow-1">
                                <div class="icon ic-xxs ic-gray ic-moon"></div>
                                <h5 class="text-dark-gray ml-20">Week off</h5>
                            </div>
                        </div>
                        <div class="border-top flex-end px-20 pb-16 pt-12 mt-20" *ngIf="isChangesMade">
                            <div class="text-decoration-underline cursor-pointer mr-10" *ngIf="!shiftTimingIsLoading"
                                (click)="onCancel()">
                                {{ 'BUTTONS.cancel' | translate }}
                            </div>
                            <button class="btn-coal" (click)="onSave()">
                                <span *ngIf="!shiftTimingIsLoading else buttonDots">{{
                                    'BUTTONS.save' | translate }}</span>
                            </button>
                        </div>
                    </div>
                </ng-container>
            </div>
            <div *ngIf="attendanceSettingsForm.get('shiftTiming').value" class="py-16 mt-12 br-6 bg-white">
                <div class="pl-20 flex-between"
                    [ngClass]="{'pb-8':attendanceSettingsForm.get('shiftTiming').value && attendanceSettingsForm.get('limitAccess').value }">
                    <div>
                        <h5 class="fw-600">{{ 'SETTINGS.limit-user' | translate }}
                        </h5>
                        <h6 class="text-dark-gray mt-4">{{ 'SETTINGS.limit-desciption'| translate }}</h6>
                    </div>
                    <div class="align-center mr-50 ph-mr-20 ml-20">
                        <div class="text-xs mr-8">{{attendanceSettingsForm.get('limitAccess').value == true ? 'on' :
                            'off'}}
                        </div>
                        <input (click)="canUpdate ? openConfirmModal(changePopup, 'limitAccess') : ''" type="checkbox"
                            class="toggle-switch toggle-active-sold" formControlName="limitAccess" id="chklimitAccess"
                            name="limitAccess" [ngClass]="{'pe-none' : !canUpdate}">
                        <label for="chklimitAccess" class="switch-label" [ngClass]="{'pe-none' : !canUpdate}"></label>
                    </div>
                </div>
                <ng-container
                    *ngIf="attendanceSettingsForm.get('shiftTiming').value && attendanceSettingsForm.get('limitAccess').value">
                    <form [formGroup]="userSettingsForm" [ngClass]="{'pe-none blinking' : shiftTimingUserIsLoading }">
                        <div class="px-30 py-20 ph-px-16 mt-2 bg-white border-top">
                            <div class="d-flex flex-wrap">
                                <ng-container *ngFor="let type of userTypeOptions">
                                    <div [ngClass]="{'pe-none' : !canUpdate}"
                                        class="form-check form-check-inline mr-10 mb-12 bg-light-pearl br-20 p-10 mt-10">
                                        <input type="radio" id="inpSelectedUser{{type.value}}" name="userOption"
                                            (change)="canUpdate ? toggleSelectUsersDropdown(type) : ''"
                                            formControlName="userOption" [value]="type.value" class="radio-check-input">
                                        <label class="text-dark-gray cursor-pointer text-large text-sm ml-6"
                                            for="inpSelectedUser{{type.value}}">{{type.displayName}}</label>
                                    </div>
                                </ng-container>
                            </div>
                            <div class="d-flex ip-flex-col" *ngIf="showUserDropdown">
                                <div class="ng-select-sm">
                                    <form-errors-wrapper [control]="userSettingsForm.controls['userList']" label="User">
                                        <div class="field-label-req">Select User(s)</div>
                                        <ng-select [virtualScroll]="true" [multiple]="true" [closeOnSelect]="false"
                                            ResizableDropdown [items]="withoutAdminList" bindLabel="fullName"
                                            bindValue="id" name="userList"
                                            (change)="canUpdate ? updateSelectedUsers($event) : ''"
                                            formControlName="userList" class="w-250 ph-w-100"
                                            placeholder="{{ 'GLOBAL.select' | translate }}"
                                            [ngClass]="{'pe-none' : !canUpdate}">
                                            <ng-template ng-label-tmp let-item="item" let-clear="clear">
                                                <span class="ic-cancel ic-dark icon ic-x-xs mr-4"
                                                    (click)="clear(item)"></span>
                                                {{item.firstName}} {{item.lastName}}
                                            </ng-template>
                                            <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                let-index="index">
                                                <div class="checkbox-container"><input type="checkbox"
                                                        id="item-{{index}}" data-automate-id="item-{{index}}"
                                                        [checked]="item$.selected"><span class="checkmark"></span><span
                                                        class="text-truncate-1 break-all">
                                                        {{item.firstName}} {{item.lastName}}</span></div>
                                            </ng-template>
                                        </ng-select>
                                    </form-errors-wrapper>
                                    <div class="w-10 mx-10 ip-d-none">
                                    </div>
                                </div>
                                <div class="mt-20 ml-20 ip-mt-10" *ngIf="selectedUsers?.length">
                                    <div class="text-sm text-dark-gray">Selected</div>
                                    <div class="d-flex flex-wrap scrollbar max-h-100px ip-ml-0 mt-6">
                                        <ng-container *ngFor="let user of selectedUsers">
                                            <div
                                                class="flex-wrap ph-mr-4 mr-10 bg-secondary px-12 py-8 br-20 align-center text-nowrap mb-20">
                                                <div class="align-center fw-semi-bold cursor-pointer text-black-200">
                                                    {{user?.fullName}}</div>
                                                <span class="icon ic-cancel ic-x-xs ml-6 ic-light-gray cursor-pointer"
                                                    (click)="canUpdate ? RemoveUserFromGroup(user) : ''">
                                                </span>
                                            </div>
                                        </ng-container>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div [ngClass]="{'d-none' : !selectedUsers?.length && !userSettingsForm?.value.userOption }"
                            class="flex-end border-top px-20 pb-16 pt-12 bg-white" *ngIf="isChanges">
                            <div class="text-decoration-underline cursor-pointer mr-10"
                                *ngIf="!shiftTimingUserIsLoading" (click)="onCancel()">
                                {{ 'BUTTONS.cancel' | translate }}
                            </div>
                            <button class="btn-coal" (click)="onSaveUser()">
                                <span *ngIf="!shiftTimingUserIsLoading else buttonDots">{{
                                    'BUTTONS.save' | translate }}</span>
                            </button>
                        </div>
                    </form>
                </ng-container>
            </div>
        </form>
        <form [formGroup]="attendanceSettingsForm">
            <div class="bg-white pl-20 py-16 mt-12 flex-between br-6">
                <div>
                    <h5 class="fw-600">{{ 'SETTINGS.mandatory-upload' | translate }} for clock in
                    </h5>
                    <h6 class="text-dark-gray pt-4">{{ 'SETTINGS.mandatory-upload-desciption'| translate }}</h6>
                </div>
                <div class="align-center mr-50 ph-mr-20 ml-20">
                    <div class="text-xs mr-8">{{attendanceSettingsForm.get('selfieUploadClockin').value == true ? 'on' :
                        'off'}}
                    </div>
                    <input (click)="canUpdate ? openConfirmModal(changePopup, 'selfieUploadClockin') : ''"
                        type="checkbox" class="toggle-switch toggle-active-sold" formControlName="selfieUploadClockin"
                        id="chkselfieUploadClockin" name="selfieUploadClockin" [ngClass]="{'pe-none' : !canUpdate}">
                    <label for="chkselfieUploadClockin" class="switch-label"
                        [ngClass]="{'pe-none' : !canUpdate}"></label>
                </div>
            </div>
            <div class="bg-white pl-20 py-16 mt-12 flex-between br-6">
                <div>
                    <h5 class="fw-600">{{ 'SETTINGS.mandatory-upload' | translate }} for clock out
                    </h5>
                    <h6 class="text-dark-gray pt-4">{{ 'SETTINGS.mandatory-upload-desciption'| translate }}</h6>
                </div>
                <div class="align-center mr-50 ph-mr-20 ml-20">
                    <div class="text-xs mr-8">{{attendanceSettingsForm.get('selfieUploadClockout').value == true ? 'on'
                        :
                        'off'}}
                    </div>
                    <input (click)="canUpdate ? openConfirmModal(changePopup, 'selfieUploadClockout') : ''"
                        type="checkbox" class="toggle-switch toggle-active-sold" formControlName="selfieUploadClockout"
                        id="chkselfieUploadClockout" name="selfieUploadClockout" [ngClass]="{'pe-none' : !canUpdate}">
                    <label for="chkselfieUploadClockout" class="switch-label"
                        [ngClass]="{'pe-none' : !canUpdate}"></label>
                </div>
            </div>
        </form>
    </div>
    <ng-template #changePopup>
        <div class="p-20">
            <h3 class="text-black-100 fw-semi-bold mb-20">{{message}}</h3>
            <div class="text-black-200 p-10 bg-light-pearl text-large br-4">Note: {{notes}}</div>
            <div class="flex-end mt-30">
                <button class="btn-gray mr-20" (click)="closePopup()" id="clkSettingsNo"
                    data-automate-id="clkSettingsNo">
                    {{ 'GLOBAL.no' | translate }}</button>
                <button class="btn-green" (click)="onSaveUser()" id="clkSettingsYes" data-automate-id="clkSettingsYes">
                    <span *ngIf="!shiftTimingUserIsLoading else buttonDots">{{ 'GLOBAL.yes' | translate
                        }}</span></button>
            </div>
        </div>
    </ng-template>
</ng-container>

<ng-template #buttonDots>
    <div class="container px-4">
        <ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-falling dot-white"></div>
        </ng-container>
    </div>
</ng-template>