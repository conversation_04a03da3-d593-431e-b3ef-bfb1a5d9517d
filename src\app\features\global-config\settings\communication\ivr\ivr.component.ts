import { Compo<PERSON>, <PERSON>E<PERSON>ter, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';

import { DIRECTION_OF_LEAD_CREATION } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import {
  assignToSort,
  getAppName,
  onlyNumbers,
  validateAllFormFields,
} from 'src/app/core/utils/common.util';
import {
  DeleteIntegration,
  FetchAgencyNameList,
  FetchIVRList,
  FetchIntegrationById,
  FetchIntegrationByIdSuccess,
  FetchIvrDetailsById,
  MakePrimary,
  UpdateIVRAccount
} from 'src/app/reducers/Integration/integration.actions';
import {
  getAgencyNameList,
  getExcelFileLink,
  getIVRList,
  getIvrDetailsById,
} from 'src/app/reducers/Integration/integration.reducer';
import { UpdateGlobalSettings } from 'src/app/reducers/global-settings/global-settings.actions';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { FetchProjectIdWithName } from 'src/app/reducers/project/project.action';
import { getProjectsIDWithName } from 'src/app/reducers/project/project.reducer';
import { FetchAllLocations } from 'src/app/reducers/site/site.actions';
import { getAllLocations } from 'src/app/reducers/site/site.reducer';
import {
  FetchAdminsAndReportees,
  FetchIVRSettingList,
  FetchUsersListForReassignment,
  UpdateIVRSettingList
} from 'src/app/reducers/teams/teams.actions';
import {
  getAdminsAndReportees,
  getIVRSetting,
  getUsersListForReassignment,
} from 'src/app/reducers/teams/teams.reducer';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';

@Component({
  selector: 'ivr',
  templateUrl: './ivr.component.html',
})
export class IvrComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  ivrSettingForm: FormGroup;
  editForm: FormGroup;
  ivrAssignmentsArray: FormArray<any>;
  selectedAccount: any;
  userList: any;
  allUserList: any;
  activeUsers: any;
  allActiveUsers: any;
  allProjectList: Array<any> = [];
  placesList: Array<any> = [];
  agencyNameList: string;
  canAdd: boolean = false;
  canDelete: boolean = false;
  canUpdate: boolean = false;
  canAssign: boolean = false;
  canAssignToAny: boolean = false;
  canViewGS: boolean = false;
  canUpdateGS: boolean = false;
  showingSection: string = 'Home';
  isShowAddAccountBtn: boolean = true;
  IVRSettingData: any;
  agentListForIVR: any;
  agentListForDialer: any;
  agentListForIVR1: any;
  agentListForDialer1: any;
  isShowEditModal: boolean = false;
  updatedInboundIntegrationList: Array<any> = [];
  updatedOutboundIntegrationList: Array<any> = [];
  globalSettingsData: any;
  directionOfLeadCreation = DIRECTION_OF_LEAD_CREATION;
  directionOfCreation: FormControl = new FormControl(0);
  getAppName = getAppName;
  onlyNumbers = onlyNumbers

  constructor(
    private store: Store<AppState>,
    public modalService: BsModalService,
    private modalRef: BsModalRef,
    private fb: FormBuilder
  ) {
    this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        if (permissions?.includes('Permissions.Integration.Create')) {
          this.canAdd = true;
        }
        if (permissions?.includes('Permissions.Integration.Delete')) {
          this.canDelete = true;
        }
        if (permissions?.includes('Permissions.Integration.Update')) {
          this.canUpdate = true;
        }
        if (permissions?.includes('Permissions.Integration.Assign')) {
          this.canAssign = true;
        }
        if (permissions?.includes('Permissions.Users.AssignToAny')) {
          this.canAssignToAny = true;
          this.store.dispatch(new FetchUsersListForReassignment());
        } else {
          this.store.dispatch(new FetchAdminsAndReportees());
        }
        if (permissions?.includes('Permissions.GlobalSettings.View')) {
          this.canViewGS = true;
        }
        if (permissions?.includes('Permissions.GlobalSettings.Update')) {
          this.canUpdateGS = true;
        }
      });

    this.editForm = this.fb.group({
      ivrAssignments: this.fb.array([
        this.fb.group({
          virtualNumber: [''],
          agencyName: [null],
          projectId: [null],
          locationId: [null],
          userIds: [null],
          showProjectLocation: false,
          showUsers: false,
          showAgency: false,
        }),
      ]),
    });

    this.ivrSettingForm = this.fb.group({
      ivrList: [null],
      dialerList: [null],
    });
    this.store.dispatch(new FetchIVRList());
    this.store.dispatch(new FetchIVRSettingList());
    this.store.dispatch(new FetchProjectIdWithName());
    this.store.dispatch(new FetchAllLocations());
    this.store.dispatch(new FetchAgencyNameList());

    this.store
      .select(getAdminsAndReportees)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userList = data;
        this.activeUsers = data?.filter((user: any) => user.isActive);
        this.activeUsers = this.activeUsers?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.activeUsers = assignToSort(this.activeUsers, '');
        this.agentListForIVR = this.activeUsers;
        this.agentListForDialer = this.activeUsers;
      });

    this.store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allUserList = data;
        this.allActiveUsers = data?.filter((user: any) => user.isActive);
        this.allActiveUsers = this.allActiveUsers?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.allActiveUsers = assignToSort(this.allActiveUsers, '');
        this.agentListForIVR = this.allActiveUsers;
        this.agentListForDialer = this.allActiveUsers;
      });

    this.store
      .select(getIVRSetting)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.IVRSettingData = data;
        let IvrUsers = this.allUserList
          .filter(
            (user: any) =>
              data?.ivrUserIds?.includes(user?.id) && user?.isActive
          )
          .map((user: any) => user?.id);
        let dialerUsers = this.allUserList
          .filter(
            (user: any) =>
              data?.dialerUserIds?.includes(user?.id) && user?.isActive
          )
          .map((user: any) => user?.id);
        this.ivrSettingForm.patchValue({
          ivrList: IvrUsers,
          dialerList: dialerUsers,
        });
        this.agentListForIVR1 = this.agentListForIVR.filter(
          (el: any) => !this.IVRSettingData?.dialerUserIds?.includes(el?.id)
        );
        this.agentListForDialer1 = this.agentListForDialer.filter(
          (el: any) => !this.IVRSettingData?.ivrUserIds?.includes(el?.id)
        );
      });

    this.ivrSettingForm.get('ivrList').valueChanges.subscribe((val) => {
      this.agentListForDialer1 = this.agentListForDialer.filter(
        (el: any) => !val?.includes(el?.id)
      );
    });

    this.ivrSettingForm.get('dialerList').valueChanges.subscribe((val) => {
      this.agentListForIVR1 = this.agentListForIVR.filter(
        (el: any) => !val?.includes(el?.id)
      );
    });
  }

  ngOnInit() {
    this.store
      .select(getIVRList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.updatedInboundIntegrationList = data
          ?.filter((item: any) => {
            return item?.leadSource == 1 && item?.ivrCallType == 0;
          })
          .sort((a: any, b: any) => Number(b.isPrimary) - Number(a.isPrimary));

        this.updatedOutboundIntegrationList = data
          ?.filter((item: any) => {
            return item?.leadSource == 1 && item?.ivrCallType == 1;
          })
          .sort((a: any, b: any) => Number(b.isPrimary) - Number(a.isPrimary));
      });

    this.store
      .select(getProjectsIDWithName)
      .pipe(takeUntil(this.stopper))
      .subscribe((res: any) => {
        this.allProjectList = res?.filter((data: any) => data.name)
          .slice()
          .sort((a: any, b: any) => a.name.localeCompare(b.name));
      });
    this.store
      .select(getAllLocations)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.placesList = data?.items
          ?.slice()
          .sort((a: any, b: any) => a.location.localeCompare(b.location));
      });

    this.store
      .select(getAgencyNameList)
      .pipe(takeUntil(this.stopper))
      .subscribe((item: any) => {
        this.agencyNameList = item
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.globalSettingsData = data;
        this.directionOfCreation.patchValue(data?.directionOfLeadCreation);
      });
  }

  reDownloadExcel(id: string) {
    this.store.dispatch(new FetchIntegrationByIdSuccess(''));
    this.store.dispatch(new FetchIntegrationById(id));
    this.store
      .select(getExcelFileLink)
      .pipe(takeUntil(this.stopper))
      .subscribe((res: any) => {
        if (res != '' && res != undefined) {
          window.open(res, '_self');
        }
      });
  }

  openEdit(id: string, accountName: string, IVRType: any) {
    this.store.dispatch(new FetchIvrDetailsById(id));
    this.isShowEditModal = true;
    this.selectedAccount = {
      id: id,
      name: accountName,
      ivrType: IVRType,
    };
    this.store
      .select(getIvrDetailsById)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (Array.isArray(data?.ivrAssignments)) {
          const ivrAssignmentControls = data?.ivrAssignments.map(
            (assignment: any) => {
              return {
                virtualNumber: assignment.virtualNumber,
                agencyName: assignment.agencyName,
                projectId: assignment.projectId,
                locationId: assignment.locationId,
                userIds: assignment?.userIds,
                showProjectLocation: false,
                showUsers: false,
                showAgency: false,
              };
            }
          );

          this.ivrAssignmentsArray = this.editForm.get(
            'ivrAssignments'
          ) as FormArray;

          // Clear existing form array
          while (this.ivrAssignmentsArray.length !== 0) {
            this.ivrAssignmentsArray.removeAt(0);
          }

          // Patch the new values
          ivrAssignmentControls.forEach((assignment: any) => {
            this.ivrAssignmentsArray.push(
              this.fb.group({
                virtualNumber: [assignment.virtualNumber],
                agencyName: [assignment.agencyName],
                projectId: [assignment.projectId],
                locationId: [assignment.locationId],
                userIds: [assignment.userIds],
                showProjectLocation: [false],
                showUsers: [false],
                showAgency: [false],
              })
            );
          });
        }
      });
  }

  updateIVRAccount() {
    if (!this.editForm.valid) {
      validateAllFormFields(this.editForm);
      return;
    }

    let payload: any = {
      id: this.selectedAccount?.id,
      ivrAssignmentDtos: this.editForm.value.ivrAssignments
        .filter(
          (item: any) =>
            item.virtualNumber !== '' && item.virtualNumber !== null
        )
        .map((item: any) => {
          const assignmentItem = {...item};
          assignmentItem.virtualNumber = String(assignmentItem.virtualNumber);
          return assignmentItem;
        }),
    };

    this.store.dispatch(new UpdateIVRAccount(payload));

    this.reset();
    this.isShowEditModal = false;
  }

  initDeleteIntegration(id: string, accountName: string) {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'delete',
      title: accountName,
      fieldType: 'account',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.store.dispatch(new DeleteIntegration(id));

          this.modalRef.hide();
        }
      });
    }
  }

  setPrimary(id: any) {
    this.store.dispatch(new MakePrimary(id));
  }

  updateCreationDirection(value: any) {
    let payload: any = {
      ...this.globalSettingsData,
      directionOfLeadCreation: value,
    };
    this.store.dispatch(new UpdateGlobalSettings(payload));
  }

  updateIVRSettingsList() {
    const ivrSettingData = this.ivrSettingForm.value;
    const prevSelectedData = [
      ...this.IVRSettingData?.ivrUserIds,
      ...this.IVRSettingData?.dialerUserIds,
    ];
    const presentSelectedData = [
      ...ivrSettingData?.ivrList,
      ...ivrSettingData?.dialerList,
    ];
    let everyTimeUserIds: any = prevSelectedData.filter(
      (el: any) => !presentSelectedData.includes(el)
    );

    let payload: any = {
      settings: [
        {
          callThrough: 0,
          userIds: everyTimeUserIds,
        },
        {
          callThrough: 1,
          userIds: ivrSettingData?.ivrList,
        },
        {
          callThrough: 2,
          userIds: ivrSettingData?.dialerList,
        },
      ],
    };
    this.store.dispatch(new UpdateIVRSettingList(payload));
    this.closeModal();
  }

  onAddVN() {
    this.ivrAssignmentsArray?.push(
      this.fb.group({
        virtualNumber: [''],
        agencyName: [null],
        projectId: [null],
        locationId: [null],
        userIds: [null],
        showProjectLocation: false,
        showUsers: false,
        showAgency: false,
      })
    );
  }

  onRemoveVN(index: number) {
    this.ivrAssignmentsArray.removeAt(index);
  }

  onToggleProjectLocation(index: number) {
    let control: any;
    control = this.ivrAssignmentsArray.controls[index] as FormGroup;
    const showSelectUserControl = control.get('showProjectLocation');
    showSelectUserControl.setValue(!showSelectUserControl.value);
  }

  onToggleAgency(index: number) {
    let control: any;
    control = this.ivrAssignmentsArray.controls[index] as FormGroup;
    const showSelectUserControl = control.get('showAgency');
    showSelectUserControl.setValue(!showSelectUserControl.value);
  }

  onToggleUsers(index: number) {
    let control: any;
    control = this.ivrAssignmentsArray.controls[index] as FormGroup;
    const showSelectUserControl = control.get('showUsers');
    showSelectUserControl.setValue(!showSelectUserControl.value);
  }

  closeModal() {
    this.modalRef.hide();
    this.reset();
  }

  reset() {
    this.ivrSettingForm.reset();
    this.editForm.reset();
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
