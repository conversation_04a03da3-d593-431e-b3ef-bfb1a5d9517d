import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output, TemplateRef, ViewChild } from '@angular/core';
import { FormBuilder, FormControl } from '@angular/forms';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { NgxMatIntlTelInputComponent } from 'ngx-mat-intl-tel-input';
import { takeUntil } from 'rxjs';
import { IntegrationSource } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { updateCountryCodeAssignment } from 'src/app/reducers/automation/automation.actions';
import { getIntegrationAssignment } from 'src/app/reducers/automation/automation.reducer';
import { getIntegrationDetails, getIntegrationDetailsIsLoading } from 'src/app/reducers/Integration/integration.reducer';

@Component({
  selector: 'country-code',
  templateUrl: './country-code.component.html',
})
export class CountryCodeComponent implements OnInit {
  @ViewChild('contactNoInput') contactNoInput!: NgxMatIntlTelInputComponent;
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  preferredCountries: any[] = [];
  integrationAssignmentData: any;
  selectedLeadSource: string;
  countryCode: FormControl = new FormControl(null);
  @Input() image: string;
  @Input() displayName: string;
  @Input() selectedAccountName: string;
  @Input() selectedIntegrations: any[];
  @Input() isBulkAssignModel: boolean;
  @Input() selectedAccountId: string;
  @Input() selectedCount: number;
  @Input() updatedIntegrationList: Array<any>;
  @Output() isShowCountryCodeModalChanged: EventEmitter<boolean> = new EventEmitter<boolean>();

  constructor(public modalService: BsModalService,
    private modalRef: BsModalRef,
    private _store: Store<AppState>,
    private cdr: ChangeDetectorRef,
  ) { }

  ngOnInit(): void {
    this._store
      .select(getIntegrationAssignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (Object.keys(data).length) {
          this.updatePreferredCountry(data?.countryCode);
        } else {
          this.updatePreferredCountry(null);
        }
      });
  }
  updatePreferredCountry(countryCode: string | null) {
    if (!this.contactNoInput) {
      return;
    }

    let matchingCountry: any = null;

    if (countryCode) {
      const countryCodeWithoutPlus = countryCode.replace('+', '');
      matchingCountry = this.contactNoInput?.allCountries?.find(
        (country: any) => country.dialCode === countryCodeWithoutPlus
      );
    }

    if (matchingCountry) {
      this.preferredCountries = [matchingCountry.iso2.toLowerCase()];
    } else if (this.integrationAssignmentData?.countries?.length) {
      const fallbackCountryCode =
        this.integrationAssignmentData.countries[0].code.toLowerCase();
      matchingCountry = this.contactNoInput.allCountries.find(
        (country: any) => country.iso2.toLowerCase() === fallbackCountryCode
      );
      this.preferredCountries = [fallbackCountryCode];
    } else {
      this.preferredCountries = ['in'];
      matchingCountry = this.contactNoInput.allCountries.find(
        (country: any) => country.iso2.toLowerCase() === 'in'
      );
    }

    this.contactNoInput.selectedCountry = matchingCountry;
    this.cdr.detectChanges();
  }

  updateCountryCode() {
    this.selectedIntegrations = this.updatedIntegrationList.filter(
      (item) => item.isSelected
    );
    let selectedIds: any = this.selectedIntegrations?.map((node: any) =>
      node?.accountId
    );
    if (selectedIds.length > 0) {
      let payload: any = {
        ids: selectedIds,
        countryCode: this.contactNoInput?.selectedCountry?.dialCode
          ? '+' + this.contactNoInput.selectedCountry.dialCode
          : null,
        source: this.selectedLeadSource,
      };
      this._store.dispatch(new updateCountryCodeAssignment(payload));
    } else {
      let payload: any = {
        ids: [this.selectedAccountId],
        countryCode: this.contactNoInput?.selectedCountry?.dialCode
          ? '+' + this.contactNoInput.selectedCountry.dialCode
          : null,
        source: this.selectedLeadSource,
      };
      this._store.dispatch(new updateCountryCodeAssignment(payload));
    }
    this.reset();
    this.hideCountryCodePopup();
  }

  reset() {
    this.updatedIntegrationList.forEach((item) => (item.isSelected = false));
    this.selectedCount = 0;
  }

  hideCountryCodePopup() {
    this.isShowCountryCodeModalChanged.emit(false);
    this.modalService.hide()
  }
}
