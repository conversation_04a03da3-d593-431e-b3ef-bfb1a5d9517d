import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  OnDestroy,
  OnInit,
  TemplateRef,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { GridApi, GridOptions } from 'ag-grid-community';
import { NotificationsService } from 'angular2-notifications';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';

import { SHOW_ENTRIES } from 'src/app/app.constants';
import { LeadSource } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  assignToSort,
  getAppName,
  getEnvDetails,
  getTenantName,
  onFilterChanged,
  validateAllFormFields,
} from 'src/app/core/utils/common.util';
import { BulkFetchFBLeads } from 'src/app/features/global-config/integration/facebook/bulk-fetch-fb-leads/bulk-fetch-fb-leads.component';
import { FacebookActionsComponent } from 'src/app/features/global-config/integration/facebook/facebook-actions/facebook-actions.component';
import { FbBulkAgencyComponent } from 'src/app/features/global-config/integration/facebook/fb-bulk-agency/fb-bulk-agency.component';
import { FbBulkCountryCodeUpdateComponent } from 'src/app/features/global-config/integration/facebook/fb-bulk-country-code-update/fb-bulk-country-code-update.component';
import { FbBulkLocationUpdateComponent } from 'src/app/features/global-config/integration/facebook/fb-bulk-location-update/fb-bulk-location-update.component';
import { FbBulkProjectUpdateComponent } from 'src/app/features/global-config/integration/facebook/fb-bulk-project-update/fb-bulk-project-update.component';
import {
  DeleteFacebookIntegration,
  FetchAgencyNameList,
  FetchExportFacebookStatus,
  FetchFbAccountForms,
  SyncAdsOfAnFBAccount,
  ToggleFBSubscription,
  UpdatePixelAccount,
} from 'src/app/reducers/Integration/integration.actions';
import {
  fetchFbAccountForms,
  fetchFbAccountFormsIsLoading,
} from 'src/app/reducers/Integration/integration.reducer';
import { FetchPriorityList } from 'src/app/reducers/automation/automation.actions';
import { getPriorityList } from 'src/app/reducers/automation/automation.reducer';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { getCustomStatusList } from 'src/app/reducers/status/status.reducer';
import {
  FetchAdminsAndReportees,
  FetchUsersListForReassignment,
} from 'src/app/reducers/teams/teams.actions';
import {
  getAdminsAndReportees,
  getUsersListForReassignment,
} from 'src/app/reducers/teams/teams.reducer';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { ExportFacebookTrackerComponent } from 'src/app/shared/components/export-facebook-tracker/export-facebook-tracker.component';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';
import { IntegrationAssignmentV2Component } from '../integration-assignment-v2/integration-assignment-v2.component';

declare let FB: any;
@Component({
  selector: 'facebook',
  templateUrl: './facebook.component.html',
})
export class FacebookComponent implements OnInit, OnDestroy, AfterViewInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  assignedUser: any[] = [];
  assignedDuplicateUser: any[] = [];
  accounts: any[] = [];
  canAdd: boolean = false;
  canDelete: boolean = false;
  canView: boolean = false;
  canAssign: boolean = false;
  canAssignToAny: boolean = false;
  selectedAccountId: string = '';
  userList: any;
  allUserList: any;
  activeUsers: any;
  allActiveUsers: any;
  assignedUserDetails: Array<string> = [];
  hasAds: boolean = false;
  isAdsExpanded: boolean = false;
  isFormsExpanded: boolean = false;
  isInternalFormsExpanded: boolean = false;
  isExternalFormsExpanded: boolean = false;
  isAdAccount: boolean = false;
  isFormAccount: boolean = false;
  subscribedAds: Array<any> = [];
  selectedAccountName: any;
  selectedAdName: any;
  filteredAccounts: any[] = [];
  searchText: string = '';
  source: LeadSource;
  moduleId: string;
  // account: any;
  // selectedAdsMap: Map<string, Set<string>> = new Map();
  // selectedFormsMap: Map<string, Set<string>> = new Map();
  initialState: any;
  gridOptions: GridOptions;
  account: any[] = [];
  gridColumnApi: any;
  gridApi: GridApi;
  filtersPayload: any;
  currOffset: number = 0;
  currentPageNumber: any;
  gridOptionsExternal: GridOptions<any>;
  gridOptionsInternal: GridOptions<any>;
  showEntriesSize: Array<number> = SHOW_ENTRIES;
  rowData: any[];
  isManualChange: boolean = true;
  lastClickedOption: any;
  cities: any = [];
  canAllowDuplicates: boolean = false;
  canEnableAllowDuplicates: boolean = false;
  canAllowSecondaryUsers: boolean = false;
  message: string = '';
  notes: string = '';
  canEnableAllowSecondaryUsers: boolean = false;
  assignedSecondaryUsers: any[] = [];
  assignedPrimaryUsers: any[] = [];
  sameAsPrimaryUsers: boolean = false;
  sameAsSelectedUsers: boolean = false;
  sameAsAbove: boolean = false;
  image: string = '../../../../assets/images/integration/facebook.svg';
  metaPixel: string = '../../../../assets/images/meta-pixel.svg';
  selectedIntegrations: any[];
  integrationDuplicateForm: FormGroup;
  integrationDualOwnerForm: FormGroup;
  pixelForm: FormGroup;
  onFilterChanged = onFilterChanged;
  getAppName = getAppName;
  isAccountsLoading: any;
  canBulkAssignment: boolean = false;
  canBulkReassign: boolean = false;
  masterLeadStatus = JSON.parse(localStorage.getItem('masterleadstatus'));
  isCustomStatusEnabled: boolean;
  customStatusList: any[] = [];
  pixelId: any;
  facebookUserId: any;
  isSaveVisible = false;
  initialFormValues: any;
  globalSettings: any;
  showLeftNav: boolean;
  selectedFbAcount: any[];

  constructor(
    private gridOptionsService: GridOptionsService,
    private gridOptionsExternalService: GridOptionsService,
    private gridOptionsInternalService: GridOptionsService,
    private router: Router,
    private _notificationService: NotificationsService,
    private store: Store<AppState>,
    private headerTitle: HeaderTitleService,
    private route: ActivatedRoute,
    private modalService: BsModalService,
    private modalRef: BsModalRef,
    private _store: Store<AppState>,
    private cdr: ChangeDetectorRef,
    private formBuilder: FormBuilder,
    private shareDataService: ShareDataService
  ) {
    this.headerTitle.setTitle('Facebook');
    if (this.route.snapshot?.routeConfig?.path == 'facebook') {
      this.initialState = {
        displayName: 'Facebook',
        image: 'assets/images/integration/facebook.svg',
        name: 'Facebook',
      };
    }
    this.pixelForm = this.formBuilder.group({
      pixelId: [null, [Validators.required]],
      status: [null, [Validators.required]],
      accessToken: [null, [Validators.required]],
    });
    this.initialFormValues = this.pixelForm.value;

    this.pixelForm.valueChanges.subscribe(() => {
      this.checkFormChanges();
    });

    this.integrationDuplicateForm = this.formBuilder.group({
      assignedUser: [null, [Validators.required]],
      assignedDuplicateUser: [null, [Validators.required]],
    });

    this.integrationDualOwnerForm = this.formBuilder.group({
      assignedPrimaryUsers: [null, [Validators.required]],
      assignedSecondaryUsers: [null, [Validators.required]],
      assignedDuplicateUser: [null, [Validators.required]],
      selectedUserType: ['Primary User(s)', [Validators.required]],
    });
  }

  ngOnInit() {
    this.store.dispatch(new FetchAgencyNameList());
    this.store.dispatch(new FetchFbAccountForms());
    this.store
      .select(getPriorityList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any[]) => {
        const filteredData = data.filter((item) => item.name === 'SubSource');
        this.moduleId = filteredData.map((item) => item.id).join(', ');
      });
    this._store
      .select(fetchFbAccountFormsIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isAccountsLoading = isLoading;
      });

    const selectAndPipe = (selector: any) =>
      this.store.select(selector).pipe(takeUntil(this.stopper));

    selectAndPipe(getPermissions).subscribe((permissions: any) => {
      const permissionsSet = new Set(permissions);
      this.canView = permissionsSet.has('Permissions.Integration.View');
      this.canDelete = permissionsSet.has('Permissions.Integration.Delete');
      this.canAdd = permissionsSet.has('Permissions.Integration.Create');
      this.canAssign = permissionsSet.has('Permissions.Integration.Assign');
      this.canBulkAssignment = permissionsSet.has(
        'Permissions.GlobalSettings.BulkAssignment'
      );
      this.canBulkReassign = permissionsSet.has(
        'Permissions.GlobalSettings.BulkReassign'
      );
      if (permissionsSet.has('Permissions.Users.AssignToAny')) {
        this.canAssignToAny = true;
        this.store.dispatch(new FetchUsersListForReassignment());
      } else {
        this.store.dispatch(new FetchAdminsAndReportees());
      }
      this.initializeGridSettings();
      this.internalFormsGridSettings();
      this.externalFormsGridSettings();
    });

    selectAndPipe(getAdminsAndReportees).subscribe((data: any) => {
      this.userList = data;
      this.activeUsers = data
        ?.filter((user: any) => user.isActive)
        .map((user: any) => ({
          ...user,
          fullName: user.firstName + ' ' + user.lastName,
        }));
      this.activeUsers = assignToSort(this.activeUsers, '');
      this.selectAllForDropdownItems(this.activeUsers);
    });

    selectAndPipe(getUsersListForReassignment).subscribe((data: any) => {
      this.allUserList = data;
      this.allActiveUsers = data
        ?.filter((user: any) => user.isActive)
        .map((user: any) => ({
          ...user,
          fullName: user.firstName + ' ' + user.lastName,
        }));
      this.allActiveUsers = assignToSort(this.allActiveUsers, '');
      this.selectAllForDropdownItems(this.allActiveUsers);
    });

    this.store
      .select(fetchFbAccountForms)
      .pipe(takeUntil(this.stopper))
      .subscribe((response: any) => {
        this.accounts = response?.map((account: any) => ({
          ...account,
          hasAds: account.ads && account.ads.length,
          hasForms: account.externalForms && account.externalForms.length,
          isAllSubscribed: this.isAllSubscribed(account),
          externalForms: account.externalForms || [],
          ads: account.ads || [],
        }));
        this.filteredAccounts = this.accounts ? [...this.accounts] : [];
      });

    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.globalSettings = data;
        this.isCustomStatusEnabled = data?.isCustomStatusEnabled;
        this.canEnableAllowDuplicates =
          data?.duplicateFeatureInfo?.isFeatureAdded;
        this.canEnableAllowSecondaryUsers = data?.isDualOwnershipEnabled;
      });

    this._store
      .select(getCustomStatusList)
      .pipe(takeUntil(this.stopper))
      .subscribe((customStatus: any) => {
        this.customStatusList = customStatus
          ?.slice()
          .sort((a: any, b: any) =>
            a?.displayName.localeCompare(b?.displayName)
          );
      });
    this.shareDataService.showLeftNav$.subscribe((show) => {
      this.showLeftNav = show;
    });
  }

  ngAfterViewInit() {
    this.cdr.detectChanges();
  }

  login() {
    this.canAdd
      ? (window.location.href = `https://integration${getEnvDetails()}/facebook?id=${localStorage?.getItem(
        'idToken'
      )}&tenant=${getTenantName()}`)
      : this._notificationService.alert(
        'No Access',
        'You dont have access for this action.'
      );
    return;
  }

  filterTable() {
    const searchText = this.searchText;
    const searchTextLower = searchText.toLowerCase();

    if (searchTextLower.length === 0) {
      this.filteredAccounts = this.accounts;
    } else {
      this.filteredAccounts = this.searchAccounts(searchText, this.accounts);
    }
  }

  objectIncludesKeyword(obj: any, keyword: string): boolean {
    for (const key in obj) {
      if (
        typeof obj[key] === 'string' &&
        obj[key].toLowerCase().includes(keyword)
      ) {
        return true;
      }
    }
    return false;
  }

  searchAccounts(searchKeyword: string, accounts: any[]): any[] {
    const keyword = searchKeyword.toLowerCase();

    // Iterate over the accounts array and filter the accounts
    return accounts.map((account: any) => {
      // Create a new account object with filtered allForms and externalForms arrays
      const newAccount: any = {
        ...account,
        allForms: account.allForms?.filter((form: any) => {
          return this.objectIncludesKeyword(form, keyword);
        }),
        internalForms: account.internalForms?.filter((form: any) => {
          return this.objectIncludesKeyword(form, keyword);
        }),
        externalForms: account.externalForms?.filter((form: any) => {
          return this.objectIncludesKeyword(form, keyword);
        }),
        ads: account.ads?.filter((ad: any) => {
          return this.objectIncludesKeyword(ad, keyword);
        }),
      };

      return newAccount;
    });
  }

  fetchFbBulkLeads() {
    let initialState: any = {};
    this.modalService.show(
      BulkFetchFBLeads,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
  }

  initDeleteIntegration(id: string, accountName: string) {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'delete',
      title: accountName,
      fieldType: 'account',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-300 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.deleteFBAccount(id);
        }
      });
    }
  }

  deleteFBAccount(id: string) {
    this.store.dispatch(new DeleteFacebookIntegration(id));
    this.modalRef.hide();
    this.router.navigate(['/global-config']);
  }

  initializeGridSettings() {
    // Ads
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.rowHeight = 60;
    this.gridOptions.columnDefs = [
      {
        headerName: 'Ads',
        field: 'Ads',
        minWidth: 150,
        valueGetter: (params) => params.data?.adName,
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">${[params?.value]}</p>`;
        },
      },
      {
        headerName: 'Ad Set',
        field: 'Ad Set',
        minWidth: 100,
        valueGetter: (params) => params.data?.adSetName,
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">${[params?.value]}</p>`;
        },
      },
      {
        headerName: 'Campaign',
        field: 'Campaign',
        width: 100,
        valueGetter: (params) => params.data?.campaignName,
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">${[params?.value]}</p>`;
        },
      },
      {
        headerName: 'Ad Account',
        field: 'Ad Account',
        minWidth: 100,
        valueGetter: (params) => params.data?.adAccountName,
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1">${[params?.value]}</p>`;
        },
      },
      {
        headerName: 'Lead Count',
        field: 'Lead Count',
        minWidth: 110,
        valueGetter: (params) => params.data?.leadCount,
        cellRenderer: (params: any) => {
          return `<p>${[params?.value]}</p>`;
        },
      },
      {
        headerName: 'Status',
        field: 'Status',
        minWidth: 120,
        valueGetter: (params) => params.data?.status,
        cellRenderer: (params: any) => {
          const status = params.value;
          const classes = this.getStatusClasses(status);
          return `<p class="${classes}">${status}</p>`;
        },
      },
      {
        headerName: 'Agency Name',
        field: 'Agency Name',
        minWidth: 120,
        valueGetter: (params) => params.data?.agencyName,
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1">${[params?.value]}</p>`;
        },
      },
      {
        headerName: 'Actions',
        field: 'Actions',
        width: 150,
        cellRenderer: FacebookActionsComponent,
      },
    ];
    if (this.canBulkReassign || this.canBulkAssignment) {
      this.gridOptions.columnDefs.unshift({
        showRowGroup: true,
        cellRenderer: 'agGroupCellRenderer',
        headerCheckboxSelection: true,
        headerCheckboxSelectionFilteredOnly: true,
        checkboxSelection: true,
        maxWidth: 50,
        suppressMovable: true,
      });
    }

    this.gridOptions.context = {
      componentParent: this,
      componentType: 'ads',
    };
  }

  internalFormsGridSettings() {
    // forms
    this.gridOptionsInternal =
      this.gridOptionsInternalService.getGridSettings(this);
    this.gridOptionsInternal.rowHeight = 44;
    this.gridOptionsInternal.columnDefs = [
      {
        headerName: 'Lead Form',
        field: 'Lead Form',
        minWidth: 180,
        valueGetter: (params: any) => params.data?.name,
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">${[params?.value]}</p>`;
        },
      },
      {
        headerName: 'Form ID',
        field: 'Form ID',
        minWidth: 120,
        valueGetter: (params: any) => params.data?.facebookId,
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1">${[params?.value]}</p>`;
        },
      },
      {
        headerName: 'Page Name',
        field: 'Page Name',
        minWidth: 120,
        valueGetter: (params: any) => params.data?.pageName,
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">${[params?.value]}</p>`;
        },
      },
      {
        headerName: 'Lead Count',
        field: 'Lead Count',
        minWidth: 100,
        valueGetter: (params: any) => params.data?.leadCount,
        cellRenderer: (params: any) => {
          return `<p>${[params?.value]}</p>`;
        },
      },
      {
        headerName: 'Status',
        field: 'Status',
        minWidth: 100,
        valueGetter: (params: any) => params.data?.status,
        cellRenderer: (params: any) => {
          const status = params.value;
          const classes = this.getStatusClasses(status);
          return `<p class="${classes}">${status}</p>`;
        },
      },
      {
        headerName: 'Agency Name',
        field: 'Agency Name',
        minWidth: 100,
        valueGetter: (params: any) => params.data?.agencyName,
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">${[params?.value]}</p>`;
        },
      },
      {
        headerName: 'Actions',
        field: 'Actions',
        minWidth: 120,
        cellRenderer: FacebookActionsComponent,
      },
    ];

    if (this.canBulkReassign || this.canBulkAssignment) {
      this.gridOptionsInternal.columnDefs.unshift({
        showRowGroup: true,
        cellRenderer: 'agGroupCellRenderer',
        headerCheckboxSelection: true,
        headerCheckboxSelectionFilteredOnly: true,
        checkboxSelection: true,
        maxWidth: 50,
        suppressMovable: true,
      });
    }

    this.gridOptionsInternal.context = {
      componentParent: this,
      componentType: 'forms',
    };
  }

  externalFormsGridSettings() {
    this.gridOptionsExternal =
      this.gridOptionsExternalService.getGridSettings(this);
    this.gridOptionsExternal.rowHeight = 44;
    this.gridOptionsExternal.columnDefs = [
      {
        headerName: 'Lead Form',
        field: 'Lead Form',
        minWidth: 180,
        valueGetter: (params: any) => params.data?.name,
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">${[params?.value]}</p>`;
        },
      },
      {
        headerName: 'Form ID',
        field: 'Form ID',
        minWidth: 120,
        valueGetter: (params: any) => params.data?.facebookId,
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1">${[params?.value]}</p>`;
        },
      },
      {
        headerName: 'Page Name',
        field: 'Page Name',
        minWidth: 120,
        valueGetter: (params: any) => params.data?.pageName,
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1">${[params?.value]}</p>`;
        },
      },
      {
        headerName: 'Lead Count',
        field: 'Lead Count',
        minWidth: 100,
        valueGetter: (params: any) => params.data?.leadCount,
        cellRenderer: (params: any) => {
          return `<p>${[params?.value]}</p>`;
        },
      },
      {
        headerName: 'Status',
        field: 'Status',
        minWidth: 100,
        valueGetter: (params: any) => params.data?.status,
        cellRenderer: (params: any) => {
          const status = params.value;
          const classes = this.getStatusClasses(status);
          return `<p class="${classes}">${status}</p>`;
        },
      },
      {
        headerName: 'Agency Name',
        field: 'Agency Name',
        minWidth: 100,
        valueGetter: (params: any) => params.data?.agencyName,
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1">${[params?.value]}</p>`;
        },
      },
      {
        headerName: 'Actions',
        field: 'Actions',
        minWidth: 130,
        cellRenderer: FacebookActionsComponent,
      },
    ];
    if (this.canBulkReassign || this.canBulkAssignment) {
      this.gridOptionsExternal.columnDefs.unshift({
        showRowGroup: true,
        cellRenderer: 'agGroupCellRenderer',
        headerCheckboxSelection: true,
        headerCheckboxSelectionFilteredOnly: true,
        checkboxSelection: true,
        maxWidth: 50,
        suppressMovable: true,
      });
    }

    this.gridOptionsExternal.context = {
      componentParent: this,
    };
  }

  toggleExpand(clickedAccount: any, key: string): void {
    this.gridApi?.deselectAll();
    this.filteredAccounts = this.filteredAccounts?.map((account: any) => {
      if (account?.id !== clickedAccount?.id) {
        return {
          ...account,
          isAdsExpanded: false,
          isFormsExpanded: false,
        };
      }
      return account;
    });
    clickedAccount[key] = !clickedAccount?.[key];
    key === 'isFormsExpanded'
      ? (clickedAccount.isAdsExpanded = false)
      : (clickedAccount.isFormsExpanded = false);
  }

  openBulkProjectModal(fbAccountName: string, isAdsExpanded: boolean) {
    let initialState: any = {
      gridApi: this.gridApi,
      isForm: !isAdsExpanded,
      fbAccountName,
      closeModal: () => {
        bulkProjectModal?.hide();
      },
    };
    var bulkProjectModal = this.modalService.show(
      FbBulkProjectUpdateComponent,
      {
        class: 'modal-350 right-modal ph-modal-unset',
        keyboard: false,
        initialState,
      }
    );
  }

  openBulkLocationModal(fbAccountName: string, isAdsExpanded: boolean) {
    let initialState: any = {
      gridApi: this.gridApi,
      isForm: !isAdsExpanded,
      fbAccountName,
      closeModal: () => {
        bulkProjectModal?.hide();
      },
    };
    var bulkProjectModal = this.modalService.show(
      FbBulkLocationUpdateComponent,
      {
        class: 'modal-350 right-modal ph-modal-unset',
        keyboard: false,
        initialState,
      }
    );
  }

  openBulkCountryCodeModal(fbAccountName: string, isAdsExpanded: boolean) {
    let initialState: any = {
      gridApi: this.gridApi,
      isForm: !isAdsExpanded,
      fbAccountName,
      closeModal: () => {
        bulkProjectModal?.hide();
      },
    };
    var bulkProjectModal = this.modalService.show(
      FbBulkCountryCodeUpdateComponent,
      {
        class: 'modal-350 right-modal ph-modal-unset',
        keyboard: false,
        initialState,
      }
    );
  }

  openBulkAgencyModal(fbAccountName: string, isAdsExpanded: boolean) {
    let initialState: any = {
      gridApi: this.gridApi,
      isForm: !isAdsExpanded,
      Integartionsource: LeadSource['Facebook'],
      fbAccountName,
      closeModal: () => {
        bulkBulkAgencyModal?.hide();
      },
    };
    var bulkBulkAgencyModal = this.modalService.show(FbBulkAgencyComponent, {
      class: 'modal-350 right-modal ph-modal-unset',
      keyboard: false,
      initialState,
    });
  }

  openBulkAssignModal(acount: any, isAdsExpanded: boolean) {
    this.store.dispatch(new FetchPriorityList());
    this.selectedFbAcount = this.filteredAccounts?.map((account: any) => account?.facebookAccountName);
    let initialState: any = {
      gridApi: this.gridApi,
      isBulkFb: true,
      isFbComponent: true,
      fbAccountName: isAdsExpanded ? this.selectedFbAcount : acount,
      integration: this.gridApi?.getSelectedNodes(),
      moduleId: this.moduleId,
      isForm: !isAdsExpanded,
      closeModal: () => {
        bulkAssignModal?.hide();
      },
    };
    var bulkAssignModal = this.modalService.show(IntegrationAssignmentV2Component, {
      class: 'modal-700 right-modal ip-modal-unset',
      keyboard: false,
      initialState
    });
  }

  getStatusClasses(status: string): string {
    switch (status) {
      case 'ACTIVE':
        return 'text-accent-green fw-700';
      case 'CAMPAIGN_PAUSED':
      case 'PAUSED':
      case 'ADSET_PAUSED':
        return 'text-dark-yellow';
      case 'WITH_ISSUES':
      case 'DISAPPROVED':
        return 'text-red';
      default:
        return '';
    }
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
  }

  openAssignmentModal(
    assignmentModal: TemplateRef<any>,
    integration: any,
    isAd: boolean = false,
    isForms: boolean = false,
  ) {
    this.modalRef = this.modalService.show(IntegrationAssignmentV2Component, {
      class: 'modal-700 right-modal ip-modal-unset',
      keyboard: false,
      initialState: {
        isAdAccount: isAd,
        isFormAccount: isForms,
        integration: integration,
        isFbComponent: true,
      }
    });
    this.selectedAccountId = integration.accountId;
    this.selectedAccountName = integration.accountName;
    this.selectedAdName = integration.adOrFormName;
    this.isAdAccount = integration.isAd;
    this.isFormAccount = integration.isForms;
    this.store.dispatch(new FetchPriorityList());
  }

  subscribeAllAds(account: any) {
    var unSubscribedAds = account?.ads.filter(
      (ad: any) => ad.isSubscribed == false
    );
    var unsubscribedAllForms = account?.allForms.filter(
      (form: any) => form.isSubscribed == false
    );
    let unsubscribedAdIds = unSubscribedAds.map((ad: any) => ad.id);
    let unSubscribedAllForms = unsubscribedAllForms.map((form: any) => form.id);
    let payload = {
      adIds: unsubscribedAdIds,
      formIds: unSubscribedAllForms,
    };
    this.store.dispatch(new ToggleFBSubscription(payload));
  }

  unsubscribeAllAds(account: any) {
    var subscribedAds = account.ads.filter(
      (ad: any) => ad.isSubscribed == true
    );
    var subscribedAllForms = account?.allForms.filter(
      (form: any) => form.isSubscribed == true
    );
    let subscribedAdIds = subscribedAds.map((ad: any) => ad.id);
    let SubscribedAllForms = subscribedAllForms.map((form: any) => form.id);
    let payload = {
      adIds: subscribedAdIds,
      formIds: SubscribedAllForms,
    };
    this.store.dispatch(new ToggleFBSubscription(payload));
  }

  isAllSubscribed(account: any) {
    var allAdsSubscribed =
      account.ads == null || account.ads.length <= 0
        ? true
        : account.ads.every((ad: any) => ad.isSubscribed);
    var allFormsSubscribed =
      account.allForms == null || account.allForms.length <= 0
        ? true
        : account.allForms.every((ad: any) => ad.isSubscribed);
    return allAdsSubscribed && allFormsSubscribed;
  }

  subscribeSingleAd(account: any, type: string) {
    if (type == 'ads') {
      let payload = {
        adIds: [account.id],
      };
      this.store.dispatch(new ToggleFBSubscription(payload));
    } else {
      let payload = {
        formIds: [account.id],
      };
      this.store.dispatch(new ToggleFBSubscription(payload));
    }
  }

  syncAds(accountId: string) {
    this.store.dispatch(new SyncAdsOfAnFBAccount(accountId));
  }

  openCustomFB() {
    const facebookDetails = {
      image: 'assets/images/integration/facebook.svg',
      displayName: 'Facebook',
      name: 'Facebook',
    };
    localStorage.setItem('integrationData', JSON.stringify(facebookDetails));

    this.router.navigate([`/global-config/integration`]);
  }

  selectAllForDropdownItems(items: any[]) {
    let allSelect = (items: any) => {
      items.forEach((element: any) => {
        element['selectedAllGroup'] = 'selectedAllGroup';
      });
    };

    allSelect(items);
  }

  checkFormChanges() {
    this.isSaveVisible =
      this.pixelForm.dirty &&
      JSON.stringify(this.pixelForm.value) !==
      JSON.stringify(this.initialFormValues);
  }

  openPixelModal(pixelModal: TemplateRef<any>, account: any) {
    this.facebookUserId = account?.facebookUserId;
    this.pixelId = account?.pixelId;
    this.modalRef = this.modalService.show(pixelModal, {
      class: 'modal-400 right-modal ip-modal-unset',
      keyboard: false,
    });

    if (account?.pixelId) {
      const selectedStatuses = account.statuses.map((status: any) => ({
        id: status.id,
        displayName: status.displayName,
        selected: false,
      }));

      this.pixelForm.patchValue({
        pixelId: account.pixelId,
        accessToken: account.conversionsAccessToken,
        status: selectedStatuses,
      });
      this.initialFormValues = this.pixelForm.value;
    }
  }

  removeStatus(status: string): void {
    const statusControl = this.pixelForm.get('status');
    if (!statusControl) return;

    const updatedStatus = (statusControl.value || []).filter(
      (item: string) => item !== status
    );
    statusControl.setValue(updatedStatus);
    statusControl.markAsDirty();
    this.pixelForm.markAsDirty();
    this.checkFormChanges();
  }

  onSubmit() {
    if (!this.pixelForm.valid) {
      validateAllFormFields(this.pixelForm);
      return;
    }
    let payload = {
      facebookUserId: this.facebookUserId,
      pixelId: this.pixelForm.value.pixelId,
      conversionAccessToken: this.pixelForm.value.accessToken,
      statusIds: this.pixelForm.value.status?.map((item: any) => item.id),
    };
    this.store.dispatch(new UpdatePixelAccount(payload));
    this.modalRef.hide();
    this.pixelForm.markAsPristine();
    this.initialFormValues = this.pixelForm.value;
    this.isSaveVisible = false;
  }

  openFacebookTracker() {
    this._store.dispatch(new FetchExportFacebookStatus(1, 10));
    this.modalService.show(ExportFacebookTrackerComponent, {
      class: 'modal-1000 modal-dialog-centered h-100 tb-modal-unset',
    });
  }

  closeModal() {
    this.modalService.hide();
    this.assignedUser = [];
    this.isAdAccount = false;
    this.isFormAccount = false;
    this.pixelForm.reset();
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
