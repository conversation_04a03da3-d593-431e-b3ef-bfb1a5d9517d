import {
  Component,
  <PERSON><PERSON><PERSON>ter,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  TemplateRef,
} from '@angular/core';
import { FormControl } from '@angular/forms';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';

import { LeadSource } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { assignToSort } from 'src/app/core/utils/common.util';
import {
  AgencyName,
  ToggleFBSubscription,
} from 'src/app/reducers/Integration/integration.actions';
import {
  fetchFbAccountForms,
  getAgencyNameList,
} from 'src/app/reducers/Integration/integration.reducer';
import {
  FetchIntegrationAssignment,
  FetchPriorityList,
  FetchUserAssignmentByEntity,
  UpdateUserAssignment,
} from 'src/app/reducers/automation/automation.actions';
import {
  getPriorityList,
  getUserAssignmentByEntity,
} from 'src/app/reducers/automation/automation.reducer';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  getAdminsAndReportees,
  getUsersListForReassignment,
} from 'src/app/reducers/teams/teams.reducer';
import { IntegrationAssignmentV2Component } from '../../integration-assignment-v2/integration-assignment-v2.component';
import { UpdateAssignmentsComponent } from '../update-assignments/update-assignments.component';

@Component({
  selector: 'ads-actions',
  templateUrl: './facebook-actions.component.html',
})
export class FacebookActionsComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  assignedUser: FormControl = new FormControl('');
  agencyName: FormControl = new FormControl(null);
  selectedAccountId: string = '';
  selectedAccountName: string;
  selectedAdName: string;
  isAdAccount: boolean;
  isFormAccount: boolean;
  assignedUserDetails: any;
  moduleId: string;
  source: LeadSource;
  agencyAccountId: string;
  agencySource: any;
  filteredAccounts: any[] = [];
  accounts: any;
  agencyNameList: any;
  allUserList: any;
  allActiveUsers: any;
  activeUsers: any;
  userList: any;

  canAssign: boolean = false;
  canAssignToAny: boolean = false;
  canEnableAllowDuplicates: boolean = false;
  canEnableAllowSecondaryUsers: boolean = false;

  params: any;
  pages: any[] = [];
  image: string = '../../../../assets/images/integration/facebook.svg';
  actionLabel: string;

  constructor(
    private store: Store<AppState>,
    private modalService: BsModalService,
    private modalRef: BsModalRef
  ) { }

  ngOnInit(): void {
    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.canEnableAllowDuplicates =
          data?.duplicateFeatureInfo?.isFeatureAdded;
        this.canEnableAllowSecondaryUsers = data?.isDualOwnershipEnabled;
      });

    const selectAndPipe = (selector: any) =>
      this.store.select(selector).pipe(takeUntil(this.stopper));

    selectAndPipe(getPermissions).subscribe((permissions: any) => {
      this.canAssign = permissions?.includes('Permissions.Integration.Assign');
      this.canAssignToAny = permissions?.includes(
        'Permissions.Users.AssignToAny'
      );
    });

    selectAndPipe(getAdminsAndReportees).subscribe((data: any) => {
      this.userList = data;
      this.activeUsers = data
        ?.filter((user: any) => user.isActive)
        .map((user: any) => ({
          ...user,
          fullName: user.firstName + ' ' + user.lastName,
        }));
      this.activeUsers = assignToSort(this.activeUsers, '');
      this.selectAllForDropdownItems(this.activeUsers);
    });

    selectAndPipe(getUsersListForReassignment).subscribe((data: any) => {
      this.allUserList = data;
      this.allActiveUsers = data
        ?.filter((user: any) => user.isActive)
        .map((user: any) => ({
          ...user,
          fullName: user.firstName + ' ' + user.lastName,
        }));
      this.allActiveUsers = assignToSort(this.allActiveUsers, '');
      this.selectAllForDropdownItems(this.allActiveUsers);
    });

    selectAndPipe(getAgencyNameList).subscribe((item: any) => {
      this.agencyNameList = item
        .filter((data: any) => data)
        .slice()
        .sort((a: any, b: any) => a.localeCompare(b));
    });

    selectAndPipe(fetchFbAccountForms).subscribe((response: any) => {
      this.accounts = response?.map((account: any) => ({
        ...account,
        hasAds: account.ads && account.ads.length,
        hasForms: account.externalForms && account.externalForms.length,
        // isAllSubscribed: this.isAllSubscribed(account),
        externalForms: account.externalForms || [],
        ads: account.ads || [],
      }));
      this.filteredAccounts = this.accounts ? [...this.accounts] : [];
    });

    this.assignedUser.valueChanges.subscribe((data: any) => {
      this.assignedUserDetails = data;
    });
  }

  agInit(params: any): void {
    this.params = params;
    if (params.context.componentType === 'forms') {
      this.actionLabel = 'Forms Action';
    } else if (params.context.componentType === 'ads') {
      this.actionLabel = 'Ads Action';
    }
  }

  selectAllForDropdownItems(items: any[]) {
    let allSelect = (items: any) => {
      items.forEach((element: any) => {
        element['selectedAllGroup'] = 'selectedAllGroup';
      });
    };

    allSelect(items);
  }

  subscribeSingleAd(account: any, type: string) {
    if (type == 'ads') {
      let payload = {
        adIds: [account?.id],
      };
      this.store.dispatch(new ToggleFBSubscription(payload));
    } else {
      let payload = {
        formIds: [account.id],
      };
      this.store.dispatch(new ToggleFBSubscription(payload));
    }
  }

  openAgentNameModal(
    agentModal: TemplateRef<any>,
    id: string,
    source: any,
    name: string,
    selectedAgencyName?: any
  ) {
    this.agencyName.reset();
    this.agencyAccountId = id;
    this.agencySource = source;
    this.selectedAccountName = name;
    this.agencyName.setValue(selectedAgencyName);
    this.modalRef = this.modalService.show(agentModal, {
      class: 'modal-300 modal-dialog-centered ph-modal-unset',
      keyboard: false,
    });
  }

  openProjectAndLocationModal(
    id: string,
    source: any,
    isAd: boolean = false,
    accountName: string,
    adOrFormName?: string
  ) {
    let initialState: any = {
      selectedAccountId: id,
      source: source,
      isAdAccount: isAd,
      selectedAccountName: accountName,
      selectedAdName: adOrFormName,
    };
    this.store.dispatch(new FetchIntegrationAssignment({ id, source }));
    this.modalRef = this.modalService.show(
      UpdateAssignmentsComponent,
      Object.assign(
        {},
        { class: 'modal-350 right-modal ip-modal-unset', initialState }
      )
    );
  }

  openAssignmentModal(
    id: string,
    isAd: boolean = false,
    isForms: boolean = false,
    integration?: any,
    adOrFormName?: string
  ) {
    this.modalRef = this.modalService.show(IntegrationAssignmentV2Component, {
      class: 'modal-700 right-modal ip-modal-unset',
      keyboard: false,
      initialState: {
        isAdAccount: isAd,
        isFormAccount: isForms,
        selectedAccountId: id,
        integration: integration,
        selectedAdName: adOrFormName,
        isFbComponent: true,
      }
    });
    this.selectedAccountId = id;
    this.selectedAdName = adOrFormName;
    this.isAdAccount = isAd;
    this.isFormAccount = isForms;
    if (id) {
      this.store.dispatch(new FetchUserAssignmentByEntity(id));
    }
    this.store
      .select(getUserAssignmentByEntity)
      .pipe(takeUntil(this.stopper))
      .subscribe((res) => {
        this.assignedUser.patchValue(res?.userIds);
        this.assignedUserDetails = res?.userIds;
      });
    this.store.dispatch(new FetchPriorityList());
    this.store
      .select(getPriorityList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any[]) => {
        const filteredData = data.filter((item) => item.name === 'SubSource');
        this.moduleId = filteredData.map((item) => item.id).join(', ');
      });
  }

  assignAccount() {
    let payload: any = {
      userIds: this.assignedUser.value,
      moduleId: this.moduleId,
      entityId: this.selectedAccountId,
    };
    this.store.dispatch(new UpdateUserAssignment(payload));
    this.closeModal();
  }

  updateAgentName() {
    let payload: any = {
      accountIds: [this.agencyAccountId],
      source: this.agencySource,
      agencyName: this.agencyName.value,
    };
    this.store.dispatch(new AgencyName(payload));
    this.closeModal();
  }

  closeModal() {
    this.modalService.hide();
    this.assignedUser.reset();
    this.isAdAccount = false;
    this.isFormAccount = false;
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
